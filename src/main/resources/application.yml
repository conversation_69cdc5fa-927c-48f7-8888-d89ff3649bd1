server:
  port: 8081
  servlet:
    session:
      timeout: 3600s
      cookie:
        name: sid
    context-path: /platform-api

spring:
  profiles:
    active: dev
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# Swagger配置
swagger:
  enable: true
  
async:
  executor:
    thread:
      core_pool_size: 5
      max_pool_size: 5
      queue_capacity: 9999
      name:
        prefix: website-async-service

mybatis:
  table:
    # create  系统启动后，会将所有的表删除掉，然后根据model中配置的结构重新建表，该操作会破坏原有数据。
    # update  系统会自动判断哪些表是新建的，哪些字段要修改类型等，哪些字段要删除，哪些字段要新增，该操作不会破坏原有数据。
    # none 	  系统不做任何处理。
    # add	  新增表/新增字段/新增索引/新增唯一约束的功能，不做做修改和删除 (只在版本1.0.9.RELEASE及以上支持)。
    auto: update
  model:
    # 扫描用于创建表的对象的包名，多个包用","隔开
    pack: cn.dahe.entity,cn.dahe.business.entity
  database:
    # 数据库类型 目前只支持mysql
    type: mysql



mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml,classpath*:com/gitee/sunchenbin/mybatis/actable/mapping/*/*.xml
  global-config:
    db-config:
      # 全局默认主键类型
      id-type: auto
      # 表名前缀
      table-prefix: t_
      # 表名是否使用驼峰转下划线命名,只对表名生效
      table-underline: true
  configuration:
    # 是否开启自动驼峰命名规则（camel case）映射，默认开启
    # 即从经典数据库列名 A_COLUMN（下划线命名） 到经典 Java 属性名 aColumn（驼峰命名） 的类似映射
    map-underscore-to-camel-case: true
    # 指定当结果集中值为 null 的时候是否调用映射对象的 Setter（Map 对象时为 put）方法，
    # 通常运用于有 Map.keySet() 依赖或 null 值初始化的情况
    # 通俗的讲，即 MyBatis 在使用 resultMap 来映射查询结果中的列，如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段，
    # 这就导致在调用到该字段的时候由于没有映射，取不到而报空指针异常
    callSettersOnNulls: false
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 通过该属性可以给包中的类注册别名，注册后在 Mapper 对应的 XML 文件中可以直接使用类名，而不用使用全限定的类名
  type-aliases-package: cn.dahe.entity,cn.dahe.business.entity



# 内容检查配置
content-check:
  api:
    url: http://localhost:8081/platform-api/sim/check/check
    timeout: 30000

api:
  auth:
    secret: dahe_collection_center_2025  # 签名密钥
    timeout: 300000                      # 超时时间（5分钟）

schedule:
  data-pull:
    all-site-search:
      enabled: true
      cron: 0 0 * * * ?
    attach-url:
      enabled: true
      cron: 0 0 * * * ?
    site-column:
      enabled: true
      cron: 0 0 * * * ?
    site-index:
      enabled: true
      cron: 0 0 * * * ?