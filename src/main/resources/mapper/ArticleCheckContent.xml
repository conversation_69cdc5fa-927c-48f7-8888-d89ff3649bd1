<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ArticleCheckContentDao">

    <!-- 文章检查结果映射 -->
    <resultMap id="ArticleContentCheckVOMap" type="cn.dahe.vo.check.ArticleContentCheckVO">
        <id column="article_id" property="articleId"/>
        <result column="a_audit_status" property="auditStatus"/>
        <result column="cleaned_title" property="cleanedTitle"/>
        <result column="cleaned_content" property="cleanedContent"/>
        <result column="compressed_title" property="htmlTitle"/>
        <result column="compressed_content" property="htmlContent"/>
        <result column="pub_time" property="pubTime"/>
        <result column="acquisition_time" property="acquisitionTime"/>
        <result column="check_time" property="checkTime"/>
        <collection property="errorObjs" ofType="cn.dahe.vo.check.ArticleCheckVO"
                    select="listArticleErrorById"
                    column="{articleId=article_id,
                            onlyShowSpecifiedErrors=only_show_specified_errors,
                            errorLevels=error_levels,
                            firstErrorTypes=first_error_types,
                            secondErrorTypes=second_error_types,
                            thirdErrorTypes=third_error_types,
                            errorAuditStatuses=error_audit_statuses}">
        </collection>
    </resultMap>

    <!-- 获取文章的错误信息 -->
    <select id="listArticleErrorById" resultType="cn.dahe.vo.check.ArticleCheckVO">
        SELECT
        id as check_id,
        error_word,
        html_error_word,
        suggest_word,
        position,
        html_position,
        ifNULl(third_error_type_id,second_error_type_id) as error_type,
        article_location,
        error_level,
        audit_status,
        context,
        marked_context
        FROM t_article_check
        WHERE article_id = #{articleId}
        <if test="onlyShowSpecifiedErrors != null and onlyShowSpecifiedErrors">
            <choose>
                <when test="errorLevels != null and errorLevels.size() > 0">
                    AND error_level IN
                    <foreach collection="errorLevels" item="level" open="(" separator="," close=")">
                        #{level}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            <choose>
                <when test="(firstErrorTypes != null and firstErrorTypes.size() > 0) or
                            (secondErrorTypes != null and secondErrorTypes.size() > 0) or
                            (thirdErrorTypes != null and thirdErrorTypes.size() > 0)">
                    AND (
                    <if test="firstErrorTypes != null and firstErrorTypes.size() > 0">
                        first_error_type_id IN
                        <foreach collection="firstErrorTypes" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="secondErrorTypes != null and secondErrorTypes.size() > 0">
                        <if test="firstErrorTypes != null and firstErrorTypes.size() > 0">OR</if>
                        second_error_type_id IN
                        <foreach collection="secondErrorTypes" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="thirdErrorTypes != null and thirdErrorTypes.size() > 0">
                        <if test="(firstErrorTypes != null and firstErrorTypes.size() > 0) or
                                     (secondErrorTypes != null and secondErrorTypes.size() > 0)">OR
                        </if>
                        third_error_type_id IN
                        <foreach collection="thirdErrorTypes" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    )
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            <choose>
                <when test="errorAuditStatuses != null and errorAuditStatuses.size() > 0">
                    AND audit_status IN
                    <foreach collection="errorAuditStatuses" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
        </if>
    </select>

    <!-- 分页查询文章检查结果 -->
    <select id="pageArticleChecks" resultMap="ArticleContentCheckVOMap">
        SELECT
        a.id as article_id,
        acc.cleaned_title,
        acc.cleaned_content,
        acc.compressed_title,
        acc.compressed_content,
        a.audit_status as a_audit_status,
        a.pub_time,
        a.acquisition_time,
        a.check_time as check_time,
        #{query.onlyShowSpecifiedErrors} as only_show_specified_errors,
        #{query.errorLevels} as error_levels,
        #{query.firstErrorTypes} as first_error_types,
        #{query.secondErrorTypes} as second_error_types,
        #{query.thirdErrorTypes} as third_error_types,
        #{query.errorAuditStatuses} as error_audit_statuses
        FROM t_article a
        LEFT JOIN t_article_check_content acc ON a.id = acc.article_id
        <where>
            exists (
            SELECT 1
            FROM t_article_check ack
            WHERE ack.article_id = a.id
            <choose>
                <when test="query.errorLevels != null and query.errorLevels.size() > 0">
                    and ack.error_level IN
                    <foreach collection="query.errorLevels" item="level" open="(" separator="," close=")">
                        #{level}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            <choose>
                <when test="(query.firstErrorTypes != null and query.firstErrorTypes.size() > 0) or
                                (query.secondErrorTypes != null and query.secondErrorTypes.size() > 0) or
                                (query.thirdErrorTypes != null and query.thirdErrorTypes.size() > 0)">
                    AND (
                    <if test="query.firstErrorTypes != null and query.firstErrorTypes.size() > 0">
                        ack.first_error_type_id IN
                        <foreach collection="query.firstErrorTypes" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="query.secondErrorTypes != null and query.secondErrorTypes.size() > 0">
                        <if test="query.firstErrorTypes != null and query.firstErrorTypes.size() > 0">OR</if>
                        ack.second_error_type_id IN
                        <foreach collection="query.secondErrorTypes" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="query.thirdErrorTypes != null and query.thirdErrorTypes.size() > 0">
                        <if test="(query.firstErrorTypes != null and query.firstErrorTypes.size() > 0) or
                                         (query.secondErrorTypes != null and query.secondErrorTypes.size() > 0)">OR
                        </if>
                        ack.third_error_type_id IN
                        <foreach collection="query.thirdErrorTypes" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    )
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            <choose>
                <when test="query.errorAuditStatuses != null and query.errorAuditStatuses.size() > 0">
                    AND ack.audit_status IN
                    <foreach collection="query.errorAuditStatuses" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            )
            <choose>
                <when test="query.articleAuditStatuses != null and query.articleAuditStatuses.size() > 0">
                    AND a.audit_status IN
                    <foreach collection="query.articleAuditStatuses" item="status" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            <choose>
                <when test="query.websiteIds != null and query.websiteIds.size() > 0">
                    AND a.website_id IN
                    <foreach collection="query.websiteIds" item="websiteId" open="(" separator="," close=")">
                        #{websiteId}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            <if test="query.keyword != null and query.keyword != ''">
                AND (ac.origin_title LIKE CONCAT('%', #{query.keyword}, '%') or ac.origin_content LIKE CONCAT('%',
                #{query.keyword}, '%'))
            </if>
            <if test="query.pubStartTime != null">
                AND a.pub_time >= #{query.pubStartTime}
            </if>
            <if test="query.pubEndTime != null">
                AND a.pub_time &lt;= #{query.pubEndTime}
            </if>
            <if test="query.checkStartTime != null">
                AND a.check_time >= #{query.checkStartTime}
            </if>
            <if test="query.checkEndTime != null">
                AND a.check_time &lt;= #{query.checkEndTime}
            </if>
        </where>
        ORDER BY a.${query.sortTypeEnum.field} ${query.sortTypeEnum.direction}
    </select>


</mapper>