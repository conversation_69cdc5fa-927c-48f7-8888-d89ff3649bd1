<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.ChkUpdateSiteIndexDao">

    <resultMap id="ChkUpdateSiteIndexVOMap" type="cn.dahe.vo.ChkUpdateSiteIndexVO">
        <id property="id" column="id"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="websiteName" column="website_name"/>
        <result property="websiteIndexUrl" column="website_index_url"/>
        <result property="isUpdated" column="is_updated"/>
        <result property="updateDays" column="update_days"/>
        <result property="continuousNotUpdateDays" column="continuous_not_update_days"/>
        <result property="lastUpdateTime" column="last_update_time"/>
        <result property="operation" column="operation"/>
        <result property="groupName" column="group_name"/>
        <result property="websiteId" column="website_id"/>
        <result property="snapshotUrl" column="snapshot_url"/>
        <result property="snapshotSignature" column="snapshot_signature"/>
        <result property="parseTime" column="parse_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateStatus" column="update_status"/>
        <result property="checkStatus" column="check_status"/>
    </resultMap>

    <!-- ==================== 首页更新检查概览 ==================== -->

    <select id="getOverviewStatistics" resultType="map">
        SELECT
            COALESCE(
                (SELECT COUNT(*) FROM chk_update_site_index WHERE is_del = 0),
                0
            ) as totalWebsiteCount,
            COALESCE(
                (SELECT COUNT(*) FROM chk_update_site_index WHERE is_del = 0 AND last_update_time &gt;= DATE_SUB(NOW(), INTERVAL 1 DAY)),
                0
            ) as updatedWebsiteCount,
            COALESCE(
                (SELECT COUNT(*) FROM chk_update_site_index WHERE is_del = 0 AND (last_update_time IS NULL OR last_update_time &lt; DATE_SUB(NOW(), INTERVAL 1 DAY))),
                0
            ) as notUpdatedWebsiteCount
    </select>

    <!-- ==================== 首页更新检查记录 ==================== -->

    <select id="selectPageWithExtInfo" resultMap="ChkUpdateSiteIndexVOMap">
        SELECT
        cusi.id,
        @row_number := @row_number + 1 as serial_number,
        w.web_name as website_name,
        w.web_url as website_index_url,
        CASE
        WHEN cusi.last_update_time &gt;= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN '是'
        ELSE '否'
        END as is_updated,
        COALESCE(DATEDIFF(NOW(), cusi.last_update_time), 0) as update_days,
        CASE
        WHEN cusi.last_update_time &gt;= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 0
        ELSE COALESCE(DATEDIFF(NOW(), cusi.last_update_time), 0)
        END as continuous_not_update_days,
        cusi.last_update_time,
        '内容明细' as operation,
        wg.group_name,
        cusi.website_id,
        cusi.snapshot_url,
        cusi.snapshot_signature,
        cusi.parse_time,
        cusi.create_time,
        CASE
        WHEN cusi.last_update_time IS NOT NULL THEN '正常'
        ELSE '异常'
        END as update_status,
        CASE
        WHEN cusi.parse_time IS NOT NULL THEN '已检查'
        ELSE '未检查'
        END as check_status
        FROM chk_update_site_index cusi
        CROSS JOIN (SELECT @row_number := 0) r
        LEFT JOIN t_website w ON cusi.website_id = w.id
        LEFT JOIN t_website_group wg ON w.group_id = wg.id
        WHERE cusi.is_del = 0
        <if test="groupName != null and groupName != ''">
            AND wg.group_name LIKE CONCAT('%', #{groupName}, '%')
        </if>
        <if test="websiteName != null and websiteName != ''">
            AND w.web_name LIKE CONCAT('%', #{websiteName}, '%')
        </if>
        <if test="websiteIndexUrl != null and websiteIndexUrl != ''">
            AND w.web_url LIKE CONCAT('%', #{websiteIndexUrl}, '%')
        </if>
        <if test="parseBeginTime != null and parseBeginTime != ''">
            AND cusi.parse_time &gt;= #{parseBeginTime}
        </if>
        <if test="parseEndTime != null and parseEndTime != ''">
            AND cusi.parse_time &lt;= #{parseEndTime}
        </if>
        <if test="createBeginTime != null and createBeginTime != ''">
            AND cusi.create_time &gt;= #{createBeginTime}
        </if>
        <if test="createEndTime != null and createEndTime != ''">
            AND cusi.create_time &lt;= #{createEndTime}
        </if>
        ORDER BY cusi.create_time DESC
    </select>

    <select id="selectDetailById" resultMap="ChkUpdateSiteIndexVOMap">
        SELECT
            cusi.id,
            6 as serial_number,
            w.web_name as website_name,
            w.web_url as website_index_url,
            CASE
                WHEN cusi.last_update_time &gt;= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN '是'
                ELSE '否'
            END as is_updated,
            COALESCE(DATEDIFF(NOW(), cusi.last_update_time), 0) as update_days,
            CASE
                WHEN cusi.last_update_time &gt;= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 0
                ELSE COALESCE(DATEDIFF(NOW(), cusi.last_update_time), 0)
            END as continuous_not_update_days,
            cusi.last_update_time,
            '内容明细' as operation,
            wg.group_name,
            cusi.website_id,
            cusi.snapshot_url,
            cusi.snapshot_signature,
            cusi.parse_time,
            cusi.create_time,
            CASE
                WHEN cusi.last_update_time IS NOT NULL THEN '正常'
                ELSE '异常'
            END as update_status,
            CASE
                WHEN cusi.parse_time IS NOT NULL THEN '已检查'
                ELSE '未检查'
            END as check_status
        FROM chk_update_site_index cusi
        LEFT JOIN t_website w ON cusi.website_id = w.id
        LEFT JOIN t_website_group wg ON w.group_id = wg.id
        WHERE cusi.id = #{id} AND cusi.is_del = 0
    </select>

</mapper>
