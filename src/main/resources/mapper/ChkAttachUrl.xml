<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.ChkAttachUrlDao">

    <resultMap id="ChkAttachUrlVOMap" type="cn.dahe.vo.ChkAttachUrlVO">
        <id property="id" column="id"/>
        <result property="websiteName" column="website_name"/>
        <result property="sourceWebsite" column="source_website"/>
        <result property="sourcePage" column="source_page"/>
        <result property="publishTime" column="publish_time"/>
        <result property="checkTime" column="check_time"/>
        <result property="operation" column="operation"/>
        <result property="attachName" column="attach_name"/>
        <result property="attachUrl" column="attach_url"/>
        <result property="attachType" column="attach_type"/>
        <result property="attachSize" column="attach_size"/>
        <result property="checkStatus" column="check_status"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- ==================== 附件检查记录 ==================== -->

    <select id="selectPageWithExtInfo" resultMap="ChkAttachUrlVOMap">
        SELECT
            cau.id,
            w.web_name as website_name,
            cau.source_website,
            cau.source_page,
            cau.publish_time,
            cau.detect_time as check_time,
            '内容' as operation,
            cau.attach_name,
            cau.attach_url,
            CASE
                WHEN cau.attach_name LIKE '%.pdf' THEN 'PDF'
                WHEN cau.attach_name LIKE '%.doc%' THEN 'DOC'
                WHEN cau.attach_name LIKE '%.xls%' THEN 'XLS'
                WHEN cau.attach_name LIKE '%.ppt%' THEN 'PPT'
                ELSE '其他'
            END as attach_type,
            '1.5MB' as attach_size,
            CASE
                WHEN cau.check_status = 1 THEN '正常'
                WHEN cau.check_status = 2 THEN '失效'
                ELSE '未检查'
            END as check_status,
            cau.create_time
        FROM chk_attach_url cau
        LEFT JOIN t_website w ON cau.website_id = w.id
        LEFT JOIN t_website_group wg ON w.group_id = wg.id
        WHERE cau.is_del = 0
        <!-- 站点类型和站点值筛选 -->
        <if test="query.siteType == 1 and query.siteValue != null and query.siteValue != ''">
            <!-- 按网站筛选 -->
            AND w.web_name LIKE CONCAT('%', #{query.siteValue}, '%')
        </if>
        <if test="query.siteType == 2 and query.siteValue != null and query.siteValue != ''">
            <!-- 按分组筛选 -->
            AND wg.group_name LIKE CONCAT('%', #{query.siteValue}, '%')
        </if>
        <!-- 时间范围筛选 -->
        <if test="query.timeType == 1">
            <!-- 按发布时间筛选 -->
            <if test="query.startTime != null and query.startTime != ''">
                AND cau.publish_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND cau.publish_time &lt;= #{query.endTime}
            </if>
        </if>
        <if test="query.timeType == 2">
            <!-- 按检测时间筛选 -->
            <if test="query.startTime != null and query.startTime != ''">
                AND cau.detect_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND cau.detect_time &lt;= #{query.endTime}
            </if>
        </if>
        <!-- 排序 -->
        <choose>
            <when test="query.timeType == 1">
                <!-- 按发布时间排序 -->
                <choose>
                    <when test="query.orderValue == 'asc'">
                        ORDER BY cau.publish_time ASC
                    </when>
                    <otherwise>
                        ORDER BY cau.publish_time DESC
                    </otherwise>
                </choose>
            </when>
            <when test="query.timeType == 2">
                <!-- 按检测时间排序 -->
                <choose>
                    <when test="query.orderValue == 'asc'">
                        ORDER BY cau.detect_time ASC
                    </when>
                    <otherwise>
                        ORDER BY cau.detect_time DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY cau.create_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectDetailById" resultMap="ChkAttachUrlVOMap">
        SELECT
            cau.id,
            w.web_name as website_name,
            cau.source_website,
            cau.source_page,
            cau.publish_time,
            cau.detect_time as check_time,
            '内容' as operation,
            cau.attach_name,
            cau.attach_url,
            CASE
                WHEN cau.attach_name LIKE '%.pdf' THEN 'PDF'
                WHEN cau.attach_name LIKE '%.doc%' THEN 'DOC'
                WHEN cau.attach_name LIKE '%.xls%' THEN 'XLS'
                WHEN cau.attach_name LIKE '%.ppt%' THEN 'PPT'
                ELSE '其他'
            END as attach_type,
            '1.5MB' as attach_size,
            CASE
                WHEN cau.check_status = 1 THEN '正常'
                WHEN cau.check_status = 2 THEN '失效'
                ELSE '未检查'
            END as check_status,
            cau.create_time
        FROM chk_attach_url cau
        LEFT JOIN t_website w ON cau.website_id = w.id
        LEFT JOIN t_website_group wg ON w.group_id = wg.id
        WHERE cau.id = #{id} AND cau.is_del = 0
    </select>

    <!-- ==================== 筛选条件 ==================== -->

    <select id="getSourceWebsites" resultType="map">
        SELECT DISTINCT
            source_website as id,
            source_website as websiteName
        FROM chk_attach_url
        WHERE is_del = 0 AND source_website IS NOT NULL AND source_website != ''
        ORDER BY source_website
    </select>

    <select id="getAttachTypes" resultType="map">
        SELECT 'PDF' as id, 'PDF文档' as typeName
        UNION ALL
        SELECT 'DOC' as id, 'Word文档' as typeName
        UNION ALL
        SELECT 'XLS' as id, 'Excel表格' as typeName
        UNION ALL
        SELECT 'PPT' as id, 'PowerPoint演示' as typeName
        UNION ALL
        SELECT '其他' as id, '其他类型' as typeName
    </select>

    <select id="getCheckStatusList" resultType="map">
        SELECT 1 as id, '正常' as statusName
        UNION ALL
        SELECT 2 as id, '失效' as statusName
        UNION ALL
        SELECT 0 as id, '未检查' as statusName
    </select>

</mapper>
