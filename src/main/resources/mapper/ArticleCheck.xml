<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ArticleCheckDao">


    <!-- 获取文章错误详情（包含错误类型名称） -->
    <select id="getArticleCheckDetail" resultType="cn.dahe.dto.ArticleCheckDto">
        SELECT ac.*,
               ac.second_error_type_id                                                             as errorType,
               IF(t3.type_name IS NOT NULL, concat(t2.type_name, '-', t3.type_name), t2.type_name) AS errorTypeName,
               el.level_name                                                                       as errorLevelName
        FROM t_article_check ac
                 LEFT JOIN t_check_error_type t2 ON t2.id = ac.second_error_type_id
                 LEFT JOIN t_check_error_type t3 ON t3.id = ac.third_error_type_id
                 LEFT JOIN t_check_error_level el ON el.id = ac.error_level
        WHERE ac.id = #{id}
    </select>

    <!-- 获取文章的错误列表 -->
    <select id="getArticleCheckList" resultType="cn.dahe.vo.check.ArticleCheckVO">
        SELECT
        ac.*,
        ac.second_error_type_id as errorType,
        IF(t3.type_name IS NOT NULL, concat(t2.type_name, '-', t3.type_name), t2.type_name) AS errorTypeName,
        el.level_name as error_level_name
        FROM t_article_check ac
        LEFT JOIN t_check_error_type t2 ON t2.id = ac.second_error_type_id
        LEFT JOIN t_check_error_type t3 ON t3.id = ac.third_error_type_id
        LEFT JOIN t_check_error_level el ON el.id = ac.error_level
        WHERE ac.article_id = #{articleId}
        <if test="auditStatus != null">
            AND ac.audit_status = #{auditStatus}
        </if>
        ORDER BY ac.position, ac.create_time
    </select>


    <select id="countErrorRecordStats" resultType="cn.dahe.dto.ArticleErrorStatsDto">
        SELECT
        COUNT(*) AS total_error_count,
        COUNT(CASE WHEN ac.error_level = 1 THEN 1 END) AS severe_error_count,
        COUNT(CASE WHEN ac.error_level = 2 THEN 1 END) AS general_error_count,
        COUNT(CASE WHEN ac.error_level = 3 THEN 1 END) AS suspected_error_count,
        COUNT(CASE WHEN ac.error_level = 4 THEN 1 END) AS custom_word_count,
        COUNT(CASE WHEN ac.error_level = 5 THEN 1 END) AS risk_tip_count,
        COUNT(DISTINCT a.id) AS article_count,
        MIN(a.pub_time) AS first_pub_time,
        IFNULL(SUM(a.word_count), 0) AS total_word_count
        FROM t_article a
        LEFT JOIN t_article_check ac ON a.id = ac.article_id
        WHERE 1 = 1
        <if test="webIdList != null and webIdList.size() > 0">
            AND a.website_id IN
            <foreach item="id" collection="webIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="errorLevelList != null and errorLevelList.size() > 0">
            AND cc.error_level IN
            <foreach item="level" collection="errorLevelList" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        <if test="pubBeginTime != null and pubBeginTime != ''">
            AND a.pub_time &gt;= #{pubBeginTime}
        </if>
        <if test="pubEndTime != null and pubEndTime != ''">
            AND a.pub_time &lt;= #{pubEndTime}
        </if>

    </select>


    <select id="countErrorArticleStats" resultType="cn.dahe.dto.ArticleErrorStatsDto">
        SELECT
        COUNT( ac.id ) AS total_error_count,-- 1. 总错误词条数（即错误记录行数）
        COUNT( DISTINCT CASE WHEN ac.error_level = 1 THEN a.id END ) AS severe_error_count,-- 2. 各类错误所涉及的文章数（去重）
        COUNT( DISTINCT CASE WHEN ac.error_level = 2 THEN a.id END ) AS general_error_count,
        COUNT( DISTINCT CASE WHEN ac.error_level = 3 THEN a.id END ) AS suspected_error_count,
        COUNT( DISTINCT CASE WHEN ac.error_level = 4 THEN a.id END ) AS custom_word_count,
        COUNT( DISTINCT CASE WHEN ac.error_level = 5 THEN a.id END ) AS risk_tip_count,
        COUNT( DISTINCT a.id ) AS article_count,
        MIN( a.pub_time ) AS first_pub_time
        FROM t_article a
        LEFT JOIN t_article_check ac ON a.id = ac.article_id
        WHERE 1 = 1
        <if test="webIdList != null and webIdList.size() > 0">
            AND a.website_id IN
            <foreach item="id" collection="webIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="errorLevelList != null and errorLevelList.size() > 0">
            AND cc.error_level IN
            <foreach item="level" collection="errorLevelList" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        <if test="pubBeginTime != null and pubBeginTime != ''">
            AND a.pub_time &gt;= #{pubBeginTime}
        </if>
        <if test="pubEndTime != null and pubEndTime != ''">
            AND a.pub_time &lt;= #{pubEndTime}
        </if>

    </select>


    <select id="countErrorWebsiteStats" resultType="cn.dahe.dto.ArticleErrorStatsDto">
        SELECT
        COUNT(DISTINCT w.id) AS website_count, -- 实际参与检测的网站数
        COUNT(DISTINCT a.id) AS article_count, -- 检测内容条数（文章数）
        SUM(a.word_count) AS total_word_count, -- 总字数
        COUNT(ac.id) AS total_error_word_count, -- 错误记录总数
        COUNT(DISTINCT CASE WHEN ac.error_level = 1 THEN w.id END) AS severe_error_website_count, -- 含严重错误的网站数
        COUNT(DISTINCT CASE WHEN ac.error_level = 2 THEN w.id END) AS general_error_count, -- 含严重错误的网站数
        COUNT(DISTINCT CASE WHEN ac.error_level = 3 THEN w.id END) AS suspected_error_count, -- 含严重错误的网站数
        COUNT(DISTINCT CASE WHEN ac.error_level = 4 THEN w.id END) AS custom_word_count, -- 含严重错误的网站数
        COUNT(DISTINCT CASE WHEN ac.error_level = 5 THEN w.id END) AS risk_tip_count -- 含严重错误的网站数
        FROM t_website w
        LEFT JOIN t_article a ON w.id = a.website_id
        LEFT JOIN t_article_check ac ON a.id = ac.article_id
        WHERE 1 = 1
        <if test="webIdList != null and webIdList.size() > 0">
            AND w.id IN
            <foreach item="id" collection="webIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="pubBeginTime != null and pubBeginTime != ''">
            AND a.pub_time &gt;= #{pubBeginTime}
        </if>
        <if test="pubEndTime != null and pubEndTime != ''">
            AND a.pub_time &lt;= #{pubEndTime}
        </if>
    </select>


</mapper>