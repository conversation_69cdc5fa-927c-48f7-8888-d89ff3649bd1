<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.WebsiteAccessRecordDao">


    <select id="listByFilters" resultType="cn.dahe.dto.WebsiteAccessOverviewDto">

        SELECT w.*,
        IFNULL(t.total_count, 0) AS totalCount,
        IFNULL(t.error_count, 0) AS errorCount
        FROM t_website w
        LEFT JOIN (
        SELECT web_id,
        COUNT(*) AS total_count,
        SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) AS error_count
        FROM t_website_access_record
        WHERE 1=1
        <if test="beginTime !=null and beginTime !='' ">
            AND check_time &gt;= #{beginTime}
        </if>
        <if test="endTime !=null and endTime !='' ">
            AND check_time &lt;= #{endTime}
        </if>
        GROUP BY web_id
        ) t ON w.id = t.web_id
        WHERE 1 = 1
        <if test="webIdList != null and webIdList.size > 0">
            AND w.id IN
            <foreach collection="webIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="groupType !=null and groupType !='' ">
            AND w.groupType = #{groupType}
        </if>
    </select>
</mapper>