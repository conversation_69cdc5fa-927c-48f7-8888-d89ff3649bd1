<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.ChkUpdateSiteColumnDao">

    <resultMap id="ChkUpdateSiteColumnVOMap" type="cn.dahe.vo.ChkUpdateSiteColumnVO">
        <id property="id" column="id"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="columnName" column="column_name"/>
        <result property="columnCategory" column="column_category"/>
        <result property="updateTime" column="update_time"/>
        <result property="checkStatus" column="check_status"/>
        <result property="continuousNotUpdateDays" column="continuous_not_update_days"/>
        <result property="checkResult" column="check_result"/>
        <result property="operation" column="operation"/>
        <result property="groupName" column="group_name"/>
        <result property="columnId" column="column_id"/>
        <result property="snapshotUrl" column="snapshot_url"/>
        <result property="snapshotSignature" column="snapshot_signature"/>
        <result property="parseTime" column="parse_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- ==================== 栏目更新检查概览 ==================== -->

    <select id="getOverviewStatistics" resultType="map">
        SELECT
            -- 栏目统计
            COALESCE(
                (SELECT COUNT(*) FROM chk_update_site_column WHERE is_del = 0),
                0
            ) as totalColumnCount,
            COALESCE(
                (SELECT COUNT(*) FROM chk_update_site_column WHERE is_del = 0 AND check_status = 1),
                0
            ) as normalColumnCount,
            COALESCE(
                (SELECT COUNT(*) FROM chk_update_site_column WHERE is_del = 0 AND check_status = 2),
                0
            ) as collectAbnormalCount,
            COALESCE(
                (SELECT COUNT(*) FROM chk_update_site_column WHERE is_del = 0 AND check_status = 3),
                0
            ) as noCheckUpdateCount,

            -- 检测结果统计
            COALESCE(
                (SELECT COUNT(*) FROM chk_update_site_column WHERE is_del = 0 AND parse_time IS NOT NULL),
                0
            ) as totalCheckResultCount,
            COALESCE(
                (SELECT COUNT(*) FROM chk_update_site_column WHERE is_del = 0 AND check_status = 1 AND DATEDIFF(NOW(), update_time) &lt;= 7),
                0
            ) as normalCheckCount,
            COALESCE(
                (SELECT COUNT(*) FROM chk_update_site_column WHERE is_del = 0 AND DATEDIFF(NOW(), update_time) &gt; 30),
                0
            ) as seriousOverdueCount,
            COALESCE(
                (SELECT COUNT(*) FROM chk_update_site_column WHERE is_del = 0 AND DATEDIFF(NOW(), update_time) BETWEEN 15 AND 30),
                0
            ) as aboutToOverdueCount
    </select>

    <!-- ==================== 栏目更新检查记录 ==================== -->

    <select id="selectPageWithExtInfo" resultMap="ChkUpdateSiteColumnVOMap">
        SELECT
        cusc.id,
        @row_number := @row_number + 1 as serial_number,
        cusc.column_name,
        cusc.column_category,
        cusc.update_time,
        CASE
        WHEN cusc.check_status = 1 THEN '正常'
        WHEN cusc.check_status = 2 THEN '异常'
        ELSE '不可访问'
        END as check_status,
        COALESCE(DATEDIFF(NOW(), cusc.update_time), 0) as continuous_not_update_days,
        CASE
        WHEN cusc.check_status = 1 THEN '通过'
        ELSE '未通过'
        END as check_result,
        '内容明细' as operation,
        cusc.group_name,
        cusc.column_id,
        cusc.snapshot_url,
        cusc.snapshot_signature,
        cusc.parse_time,
        cusc.create_time
        FROM chk_update_site_column cusc
        CROSS JOIN (SELECT @row_number := 0) r
        WHERE cusc.is_del = 0
        <if test="query.columnName != null and query.columnName != ''">
            AND cusc.column_name LIKE CONCAT('%', #{query.columnName}, '%')
        </if>
        <if test="query.columnCategory != null and query.columnCategory != ''">
            AND cusc.column_category LIKE CONCAT('%', #{query.columnCategory}, '%')
        </if>
        <if test="query.checkStartTime != null and query.checkStartTime != ''">
            AND cusc.parse_time &gt;= #{query.checkStartTime}
        </if>
        <if test="query.checkEndTime != null and query.checkEndTime != ''">
            AND cusc.parse_time &lt;= #{query.checkEndTime}
        </if>
        <if test="query.updateStartTime != null and query.updateStartTime != ''">
            AND cusc.update_time &gt;= #{query.updateStartTime}
        </if>
        <if test="query.updateEndTime != null and query.updateEndTime != ''">
            AND cusc.update_time &lt;= #{query.updateEndTime}
        </if>
        <if test="query.websiteUrl != null and query.websiteUrl != ''">
            AND cusc.website_url LIKE CONCAT('%', #{query.websiteUrl}, '%')
        </if>
        <if test="query.notUpdateDays != null">
            AND DATEDIFF(NOW(), cusc.update_time) &gt;= #{query.notUpdateDays}
        </if>
        <if test="query.checkDate != null and query.checkDate != ''">
            AND DATE(cusc.parse_time) = #{query.checkDate}
        </if>
        ORDER BY cusc.create_time DESC
    </select>

    <select id="selectDetailById" resultMap="ChkUpdateSiteColumnVOMap">
        SELECT
            cusc.id,
            1 as serial_number,
            cusc.column_name,
            cusc.column_category,
            cusc.update_time,
            CASE
                WHEN cusc.check_status = 1 THEN '正常'
                WHEN cusc.check_status = 2 THEN '异常'
                ELSE '不可访问'
            END as check_status,
            COALESCE(DATEDIFF(NOW(), cusc.update_time), 0) as continuous_not_update_days,
            CASE
                WHEN cusc.check_status = 1 THEN '通过'
                ELSE '未通过'
            END as check_result,
            '内容明细' as operation,
            cusc.group_name,
            cusc.column_id,
            cusc.snapshot_url,
            cusc.snapshot_signature,
            cusc.parse_time,
            cusc.create_time
        FROM chk_update_site_column cusc
        WHERE cusc.id = #{id} AND cusc.is_del = 0
    </select>

    <!-- ==================== 筛选条件 ==================== -->

    <select id="getColumnCategories" resultType="map">
        SELECT DISTINCT
            column_category as id,
            column_category as categoryName
        FROM chk_update_site_column
        WHERE is_del = 0 AND column_category IS NOT NULL AND column_category != ''
        ORDER BY column_category
    </select>

    <select id="getCheckStatusList" resultType="map">
        SELECT 1 as id, '正常' as statusName
        UNION ALL
        SELECT 2 as id, '异常' as statusName
        UNION ALL
        SELECT 3 as id, '不可访问' as statusName
    </select>

</mapper>
