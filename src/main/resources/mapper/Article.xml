<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ArticleDao">

    <resultMap id="BaseResultMap" type="cn.dahe.dto.ArticleDto">
        <id column="id" property="id"/>
        <result column="website_id" property="websiteId"/>
        <result column="website_name" property="websiteName"/>
        <result column="title" property="title"/>

        <result column="source" property="source"/>
        <result column="pub_time" property="pubTime"/>
        <result column="write_time" property="writeTime"/>
        <result column="link_url" property="linkUrl"/>
        <result column="snapshot" property="snapshot"/>
        <result column="editor" property="editor"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="disposal_status" property="disposalStatus"/>
        <result column="disposal_remark" property="disposalRemark"/>
        <result column="rectify_status" property="rectifyStatus"/>
        <result column="word_count" property="wordCount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="check_time" property="checkTime"/>

        <result column="marked_title" property="markedTitle"/>
        <result column="marked_content" property="markedContent"/>

        <collection property="articleCheckDtoList" ofType="cn.dahe.dto.ArticleCheckDto">
            <id column="cc_id" property="id"/>
            <result column="article_id" property="articleId"/>
            <result column="error_word" property="errorWord"/>
            <result column="suggest_word" property="suggestWord"/>
            <result column="error_type" property="errorType"/>
            <result column="error_level" property="errorLevel"/>
            <result column="position" property="position"/>
            <result column="cc_create_time" property="createTime"/>
            <result column="cc_check_time" property="checkTime"/>
            <result column="cc_audit_status" property="auditStatus"/>
        </collection>

    </resultMap>


    <select id="listByFilters" resultMap="BaseResultMap">
        SELECT
        a.*,
        articleCheckContent.marked_title,
        articleCheckContent.marked_content,

        articleCheck.id AS cc_id,
        articleCheck.article_id,
        articleCheck.error_word,
        articleCheck.suggest_word,
        articleCheck.error_level,
        articleCheck.position,
        articleCheck.check_time AS cc_check_time,
        articleCheck.create_time AS cc_create_time,
        articleCheck.audit_status AS cc_audit_status
        FROM t_article a
        LEFT JOIN t_article_check_content articleCheckContent ON a.id = articleCheckContent.article_id
        LEFT JOIN t_article_check articleCheck ON a.id = articleCheck.article_id
        WHERE 1 = 1

        <if test="groupType != null and groupType != ''">
            AND a.group_type = #{groupType}
        </if>

        <if test="beginTime != null and beginTime != ''">
            AND a.check_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND a.check_time &lt;= #{endTime}
        </if>

        <if test="source != null and source != ''">
            AND a.source LIKE CONCAT('%', #{source}, '%')
        </if>

        <if test="webIdList != null and webIdList.size() > 0">
            AND a.website_id IN
            <foreach item="id" collection="webIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="auditStatusList != null and auditStatusList.size() > 0">
            AND a.audit_status IN
            <foreach item="status" collection="auditStatusList" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        <if test="disposalStatusList != null and disposalStatusList.size() > 0">
            AND a.disposal_status IN
            <foreach item="status" collection="disposalStatusList" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        <if test="rectifyStatusList != null and rectifyStatusList.size() > 0">
            AND a.rectify_status IN
            <foreach item="status" collection="rectifyStatusList" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        <if test="errorLevelList != null and errorLevelList.size() > 0">
            AND articleCheck.error_level IN
            <foreach item="level" collection="errorLevelList" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>

        <if test="errorTypeList != null and errorTypeList.size() > 0">
            AND articleCheck.error_type IN
            <foreach item="type" collection="errorTypeList" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>

        <if test="errorAuditStatusList != null and errorAuditStatusList.size() > 0">
            AND articleCheck.audit_status IN
            <foreach item="auditStatus" collection="errorAuditStatusList" open="(" separator="," close=")">
                #{auditStatus}
            </foreach>
        </if>

        <if test="errorWord != null and errorWord != ''">
            AND articleCheck.error_word LIKE CONCAT('%', #{errorWord}, '%')
        </if>

        <if test="suggestWord != null and suggestWord != ''">
            AND articleCheck.suggest_word LIKE CONCAT('%', #{suggestWord}, '%')
        </if>

        <if test="(cleanContent != null and cleanContent != '') or (cleanTitle != null and cleanTitle != '')">
            AND (
            <if test="cleanContent != null and cleanContent != ''">
                articleCheckContent.clean_content LIKE CONCAT('%', #{cleanContent}, '%')
            </if>
            <if test="(cleanContent != null and cleanContent != '') and (cleanTitle != null and cleanTitle != '')">
                OR
            </if>
            <if test="cleanTitle != null and cleanTitle != ''">
                articleCheckContent.clean_title LIKE CONCAT('%', #{cleanTitle}, '%')
            </if>
            )
        </if>

    </select>


</mapper> 