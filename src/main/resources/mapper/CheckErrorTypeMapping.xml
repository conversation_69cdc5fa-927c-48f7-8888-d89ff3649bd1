<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.CheckErrorTypeMappingDao">
    
    <resultMap id="BaseResultMap" type="cn.dahe.entity.CheckErrorTypeMapping">
        <id column="id" property="id"/>
        <result column="source_id" property="sourceId"/>
        <result column="source_error_code" property="sourceErrorCode"/>
        <result column="source_error_name" property="sourceErrorName"/>
        <result column="target_type_id" property="targetTypeId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="VOResultMap" type="cn.dahe.vo.check.CheckErrorTypeMappingVO" extends="BaseResultMap">
        <result column="source_name" property="sourceName"/>
        <result column="target_type_name" property="targetTypeName"/>
        <result column="target_type_level" property="targetTypeLevel"/>
    </resultMap>

    <select id="getMappingList" resultMap="VOResultMap">
        SELECT m.*, s.source_name, t.type_name as target_type_name, t.level as target_type_level
        FROM t_check_error_type_mapping m
        LEFT JOIN t_check_source s ON s.id = m.source_id
        LEFT JOIN t_check_error_type t ON t.id = m.target_type_id
        ORDER BY m.create_time DESC
    </select>

    <select id="getMappingBySourceError" resultMap="VOResultMap">
        SELECT m.*, s.source_name, t.type_name as target_type_name, t.level as target_type_level
        FROM t_check_error_type_mapping m
        LEFT JOIN t_check_source s ON s.id = m.source_id
        LEFT JOIN t_check_error_type t ON t.id = m.target_type_id
        WHERE s.source_code = #{sourceCode}
        AND m.source_error_code = #{sourceErrorCode}
    </select>

</mapper> 