<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.CheckTaskDao">
    
    <resultMap id="BaseResultMap" type="cn.dahe.entity.CheckTask">
        <id column="id" property="id"/>
        <result column="website_id" property="websiteId"/>
        <result column="article_id" property="articleId"/>
        <result column="check_strategy" property="checkStrategy"/>
        <result column="task_status" property="taskStatus"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="fail_reason" property="failReason"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

</mapper> 