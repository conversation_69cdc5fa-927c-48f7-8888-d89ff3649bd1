<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.dahe.dao.AttachmentDao">

    <resultMap type="cn.dahe.entity.Attachment" id="attachmentMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="alias" column="alias"/>
        <result property="suffix" column="suffix"/>
        <result property="size" column="size"/>
        <result property="url" column="url"/>
        <result property="reportId" column="report_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!--更新附件的上报信息ID-->
    <update id="updateReporterIdByIds">
        update t_attachment set report_id=#{reportId}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>