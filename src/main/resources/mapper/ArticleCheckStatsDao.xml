<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dahe.dao.ArticleCheckStatsDao">

    <select id="pageStatsByWebsite" resultType="cn.dahe.vo.WebsiteArticleCheckStatsVO">
        SELECT
        ta.website_id,
        tw.web_name as website_name,
        tw.web_url as website_url,
        tw.group_id,
        count(distinct ta.id) as article_count,
        IFNULL(sum(ta.word_count),0) as article_length,
        count(distinct tac.id) as check_count,
        count(distinct IF(tac.error_level = 1, tac.id, null)) as lv1_check_count,
        count(distinct IF(tac.error_level = 2, tac.id, null)) as lv2_check_count,
        count(distinct IF(tac.error_level = 3, tac.id, null)) as lv3_check_count,
        count(distinct IF(tac.error_level = 4, tac.id, null)) as lv4_check_count,
        count(distinct IF(tac.error_level = 5, tac.id, null)) as lv5_check_count
        FROM t_website tw
        left join t_article ta on ta.website_id = tw.id
        left join t_article_check tac on ta.id = tac.article_id
        <where>
            <choose>
                <when test="query.websiteIds != null and query.websiteIds.size() > 0">
                    AND ta.website_id IN
                    <foreach collection="query.websiteIds" item="webId" open="(" separator="," close=")">
                        #{webId}
                    </foreach>
                </when>
                <otherwise>
                    and false
                </otherwise>
            </choose>
            <choose>
                <when test="query.errorLevels != null and query.errorLevels.size() > 0">
                    AND tac.error_level IN
                    <foreach collection="query.errorLevels" item="level" open="(" separator="," close=")">
                        #{level}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            <if test="query.pubBeginDate != null">
                AND ta.pub_time >= #{query.pubBeginDate}
            </if>
            <if test="query.pubEndDate != null">
                AND ta.pub_time &lt;= #{query.pubEndDate}
            </if>
        </where>
        GROUP BY ta.website_id, tw.group_id
    </select>
    <select id="queryTotalStats" resultType="cn.dahe.vo.WebsiteArticleCheckTotalStatsVO">
        SELECT
        count(distinct ta.website_id) as website_count,
        count(distinct ta.id) as article_count,
        IFNULL(sum(ta.word_count),0) as article_length,
        count(distinct tac.id) as check_count,
        count(distinct tacw.id) as check_word_count,
        count(distinct IF(tacw.error_level = 1, ta.website_id, null)) as lv1_website_count,
        count(distinct IF(tacw.error_level = 2, ta.website_id, null)) as lv2_website_count,
        count(distinct IF(tacw.error_level = 3, ta.website_id, null)) as lv3_website_count,
        count(distinct IF(tacw.error_level = 4, ta.website_id, null)) as lv4_website_count,
        count(distinct IF(tacw.error_level = 5, ta.website_id, null)) as lv5_website_count
        FROM
        t_article ta
        left join t_article_check tac on ta.id = tac.article_id
        left join t_article_check_word tacw on tac.word_id = tacw.id
        <where>
            <choose>
                <when test="query.websiteIds != null and query.websiteIds.size() > 0">
                    AND ta.website_id IN
                    <foreach collection="query.websiteIds" item="webId" open="(" separator="," close=")">
                        #{webId}
                    </foreach>
                </when>
                <otherwise>
                    and false
                </otherwise>
            </choose>
            <choose>
                <when test="query.errorLevels != null and query.errorLevels.size() > 0">
                    AND tac.error_level IN
                    <foreach collection="query.errorLevels" item="level" open="(" separator="," close=")">
                        #{level}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            <if test="query.pubBeginDate != null">
                AND ta.pub_time >= #{query.pubBeginDate}
            </if>
            <if test="query.pubEndDate != null">
                AND ta.pub_time &lt;= #{query.pubEndDate}
            </if>
        </where>
    </select>
    <select id="queryWordTotalStats" resultType="cn.dahe.vo.WebsiteArticleCheckWordTotalStatsVO">
        select
        count(distinct word_id) as check_word_count,
        count(distinct IF(tacw.error_level = 1, word_id, null)) as lv1_check_word_count,
        count(distinct IF(tacw.error_level = 2, word_id, null)) as lv2_check_word_count,
        count(distinct IF(tacw.error_level = 3, word_id, null)) as lv3_check_word_count,
        count(distinct IF(tacw.error_level = 4, word_id, null)) as lv4_check_word_count,
        count(distinct IF(tacw.error_level = 5, word_id, null)) as lv5_check_word_count
        from t_article_check tac
        left join t_article ta on tac.article_id = ta.id
        left join t_article_check_word tacw on tac.word_id = tacw.id
        <where>
            <choose>
                <when test="query.errorLevels != null and query.errorLevels.size() > 0">
                    AND tacw.error_level IN
                    <foreach collection="query.errorLevels" item="level" open="(" separator="," close=")">
                        #{level}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            <choose>
                <when test="query.filterStatuses != null and query.filterStatuses.size() > 0">
                    AND tacw.is_filtered IN
                    <foreach collection="query.filterStatuses" item="filterStatus" open="(" separator="," close=")">
                        #{filterStatus}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            <choose>
                <when test="query.websiteIds != null and query.websiteIds.size() > 0">
                    AND ta.website_id IN
                    <foreach collection="query.websiteIds" item="webId" open="(" separator="," close=")">
                        #{webId}
                    </foreach>
                </when>
                <otherwise>
                    and false
                </otherwise>
            </choose>
            <if test="query.pubBeginDate != null">
                AND ta.pub_time >= #{query.pubBeginDate}
            </if>
            <if test="query.pubEndDate != null">
                AND ta.pub_time &lt;= #{query.pubEndDate}
            </if>
        </where>
    </select>
    <resultMap id="WebsiteArticleCheckWordStatsVOMap" type="cn.dahe.vo.WebsiteArticleCheckWordStatsVO">
        <result column="word_id" property="wordId"/>
        <result column="error_word" property="errorWord"/>
        <result column="suggest_word" property="suggestWord"/>
        <result column="is_filtered" property="isFiltered"/>
        <result column="error_level" property="errorLevel"/>
        <result column="error_type" property="errorType"/>
        <result column="check_count" property="checkCount"/>
        <result column="website_count" property="websiteCount"/>
        <collection property="websites" ofType="cn.dahe.vo.WebsiteArticleCheckWordStatsVO$WebsiteInfo">
            <result column="website_id" property="websiteId"/>
            <result column="website_name" property="websiteName"/>
        </collection>
    </resultMap>
    <select id="pageWordStatsByWebsite" resultMap="WebsiteArticleCheckWordStatsVOMap">
        WITH website_data AS (
        select
        tacw.id as word_id,
        tacw.error_word,
        tacw.suggest_word,
        tacw.is_filtered,
        tacw.error_level,
        ifNULl(tacw.third_error_type_id,tacw.second_error_type_id) as error_type,
        count(tac.id) as check_count,
        count(distinct ta.website_id) as website_count,
        tw.id as website_id,
        tw.web_name as website_name
        from t_article_check_word tacw
        left join t_article_check tac on tac.word_id = tacw.id
        left join t_article ta on tac.article_id = ta.id
        left join t_website tw on ta.website_id = tw.id
        <where>
            <choose>
                <when test="query.filterStatuses != null and query.filterStatuses.size() > 0">
                    AND tacw.is_filtered IN
                    <foreach collection="query.filterStatuses" item="filterStatus" open="(" separator="," close=")">
                        #{filterStatus}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            <choose>
                <when test="query.errorLevels != null and query.errorLevels.size() > 0">
                    AND tacw.error_level IN
                    <foreach collection="query.errorLevels" item="level" open="(" separator="," close=")">
                        #{level}
                    </foreach>
                </when>
                <otherwise>
                    AND false
                </otherwise>
            </choose>
            <choose>
                <when test="query.websiteIds != null and query.websiteIds.size() > 0">
                    AND ta.website_id IN
                    <foreach collection="query.websiteIds" item="webId" open="(" separator="," close=")">
                        #{webId}
                    </foreach>
                </when>
                <otherwise>
                    and false
                </otherwise>
            </choose>
            <if test="query.pubBeginDate != null">
                AND ta.pub_time >= #{query.pubBeginDate}
            </if>
            <if test="query.pubEndDate != null">
                AND ta.pub_time &lt;= #{query.pubEndDate}
            </if>
        </where>
        group by tacw.id, tw.id, tw.web_name
        )
        SELECT * FROM website_data ORDER BY check_count DESC
    </select>

</mapper> 