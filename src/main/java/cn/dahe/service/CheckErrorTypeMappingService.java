package cn.dahe.service;

import cn.dahe.entity.CheckErrorTypeMapping;
import cn.dahe.vo.check.CheckErrorTypeMappingVO;

import java.util.List;

/**
 * 错误类型映射Service
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface CheckErrorTypeMappingService extends BaseService<CheckErrorTypeMapping> {

    /**
     * 获取映射关系列表
     */
    List<CheckErrorTypeMappingVO> getMappingList();

    /**
     * 根据来源和错误码获取映射关系
     */
    CheckErrorTypeMappingVO getMappingBySourceError(String sourceCode, String sourceErrorCode);

    /**
     * 批量保存映射关系
     */
    boolean saveBatchMapping(List<CheckErrorTypeMapping> mappings);
} 