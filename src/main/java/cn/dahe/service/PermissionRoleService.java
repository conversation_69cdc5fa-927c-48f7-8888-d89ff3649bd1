package cn.dahe.service;

import cn.dahe.dto.Result;
import cn.dahe.entity.PermissionRole;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface PermissionRoleService extends IService<PermissionRole> {
    /**
     * 添加
     *
     * @param permissionRole
     * @param user
     * @return
     */
    Result<String> save(PermissionRole permissionRole, LoginUserVO user);

    /**
     * 编辑
     *
     * @param id
     * @param permissionRole
     * @return
     */
    Result<String> update(String id, PermissionRole permissionRole);


    /**
     * 根据角色id删除
     *
     * @param roleId
     * @return
     */
    Result<String> removeByRoleId(Integer roleId);


    /**
     * 通过角色ID与权限IDS保存
     *
     * @param roleId
     * @param permissionIds
     * @return
     */
    Result<String> saveByRoleIdAndPermissionIds(Integer roleId, String permissionIds);

    /**
     * 通过角色ID查找
     *
     * @param roleIdId
     * @return
     */
    List<PermissionRole> listByRoleId(Integer roleIdId);
}