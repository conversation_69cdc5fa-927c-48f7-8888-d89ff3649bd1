package cn.dahe.service;

import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.ChkUpdateSiteColumn;
import cn.dahe.query.ChkUpdateSiteColumnQuery;
import cn.dahe.vo.ChkUpdateSiteColumnVO;
import cn.dahe.vo.LoginUserVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 栏目更新检查Service - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface ChkUpdateSiteColumnService extends BaseService<ChkUpdateSiteColumn> {

    // ==================== 栏目更新检查概览 ====================

    /**
     * 获取栏目更新检查概览统计
     * 包含：栏目总数、正常、异常、不可访问
     *
     * @param query 查询参数
     * @return 统计数据
     */
    Map<String, Object> getOverviewStatistics(ChkUpdateSiteColumnQuery query);

    // ==================== 栏目更新检查记录 ====================

    /**
     * 分页查询栏目更新检查记录
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageResult<ChkUpdateSiteColumnVO> page(ChkUpdateSiteColumnQuery query);

    /**
     * 根据ID获取栏目更新检查详情
     *
     * @param id ID
     * @return 详情
     */
    ChkUpdateSiteColumnVO get(Long id);


    // ==================== 数据导出 ====================

    /**
     * 导出栏目更新检查记录
     *
     * @param query 查询参数
     * @param user 当前用户
     * @param response HTTP响应
     */
    void export(ChkUpdateSiteColumnQuery query, LoginUserVO user, HttpServletResponse response) throws IOException;
}
