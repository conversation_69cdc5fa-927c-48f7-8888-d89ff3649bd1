package cn.dahe.service;

import cn.dahe.dto.ArticleCheckContentDto;
import cn.dahe.dto.ArticleErrorStatsDto;
import cn.dahe.dto.ContentCheckResultDto;
import cn.dahe.dto.PageResult;
import cn.dahe.entity.ArticleCheck;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.query.ArticleCheckQuery;
import cn.dahe.query.ArticleCheckStatQuery;
import cn.dahe.vo.check.ArticleCheckVO;

import java.util.List;

/**
 * 文章错误检查Service
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface ArticleCheckService extends BaseService<ArticleCheck> {

    /**
     * 处理并保存文章的HTML内容
     *
     * @param articleId 文章ID
     * @param title     文章标题
     * @param content   文章内容
     * @return 处理后的HTML结果
     */
    ArticleCheckContentDto initPushArticle(Long articleId, String title, String content);

    /**
     * 执行文章内容校对
     *
     * @param title   纯文本标题
     * @param content 纯文本内容
     * @return 校对结果DTO，包含校对状态和结果
     */
    ContentCheckResultDto executeContentCheck(String title, String content);

    /**
     * 处理校对结果并更新位置信息
     *
     * @param articleId     文章ID
     * @return 处理后的校对结果
     */
    void processCheckResults(Long articleId, ArticleCheckContentDto checkContentDto,ContentCheckResultDto checkResult);





    /**
     * 更新审核状态
     *
     * @param checkIds    检查ID列表
     * @param auditStatus 审核状态
     * @return 处理结果
     */
    Boolean updateErrorAuditStatus(Long articleId, List<Long> checkIds, AuditStatusEnum auditStatus);


    /**
     * 网站错误统计信息列表
     * 查看网站的错误情况
     *
     * @param articleQuery
     * @return
     */
    PageResult<ArticleErrorStatsDto> pageWebsiteErrorStatistics(ArticleCheckStatQuery articleQuery);

    /**
     * @param articleId
     * @param query
     * @return
     */
    List<ArticleCheckVO> getArticleCheckList(Long articleId, ArticleCheckQuery query);
} 