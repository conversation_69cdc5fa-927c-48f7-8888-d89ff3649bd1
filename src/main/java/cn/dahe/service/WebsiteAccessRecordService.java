package cn.dahe.service;

import cn.dahe.dto.PageResult;
import cn.dahe.dto.WebsiteAccessOverviewDto;
import cn.dahe.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.WebsiteAccessRecord;
import cn.dahe.query.WebsiteAccessRecordQuery;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface WebsiteAccessRecordService extends IService<WebsiteAccessRecord> {

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<WebsiteAccessRecord> page(WebsiteAccessRecordQuery query);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<WebsiteAccessOverviewDto> pageStats(WebsiteAccessRecordQuery query);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    WebsiteUpdateStatsDto totalStats(WebsiteAccessRecordQuery query);
}