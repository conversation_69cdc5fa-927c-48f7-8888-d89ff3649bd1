package cn.dahe.service.impl;

import cn.dahe.dao.PermissionRoleDao;
import cn.dahe.dto.Result;
import cn.dahe.entity.PermissionRole;
import cn.dahe.service.PermissionRoleService;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@AllArgsConstructor
public class PermissionRoleServiceImpl extends BaseServiceImpl<PermissionRoleDao, PermissionRole> implements PermissionRoleService {

    //123
    @Override
    public Result<String> save(PermissionRole permissionRole, LoginUserVO user) {
        baseMapper.insert(permissionRole);
        return Result.ok();
    }

    @Override
    public Result<String> update(String id, PermissionRole vo) {
        PermissionRole permissionRole = baseMapper.selectById(id);
        if (permissionRole == null) {
            return Result.error("请选择正确的数据");
        }
        boolean update = updateById(permissionRole);
        if (update) {
            return Result.ok();
        }
        return Result.error();
    }

    @Override
    public Result<String> removeByRoleId(Integer roleId) {
        QueryWrapper<PermissionRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleId);
        if (!remove(queryWrapper)) {
            return Result.error();

        }
        return Result.ok();
    }

    @Override
    public Result<String> saveByRoleIdAndPermissionIds(Integer roleId, String permissionIds) {
        List<Integer> collect = Arrays.stream(permissionIds.split(",")).map(Integer::parseInt).distinct().collect(Collectors.toList());
        for (Integer permissionId : collect) {
            PermissionRole permissionRole = new PermissionRole();
            permissionRole.setRoleId(roleId);
            permissionRole.setPermissionId(permissionId);
            this.save(permissionRole);
        }
        return Result.ok();
    }

    @Override
    public List<PermissionRole> listByRoleId(Integer roleIdId) {
        QueryWrapper<PermissionRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", roleIdId);
        return list(queryWrapper);
    }
}