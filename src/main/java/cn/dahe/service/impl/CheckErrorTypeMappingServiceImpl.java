package cn.dahe.service.impl;

import cn.dahe.dao.CheckErrorTypeMappingDao;
import cn.dahe.entity.CheckErrorTypeMapping;
import cn.dahe.service.CheckErrorTypeMappingService;
import cn.dahe.vo.check.CheckErrorTypeMappingVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 错误类型映射Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
public class CheckErrorTypeMappingServiceImpl extends BaseServiceImpl<CheckErrorTypeMappingDao, CheckErrorTypeMapping> implements CheckErrorTypeMappingService {

    @Override
    public List<CheckErrorTypeMappingVO> getMappingList() {
        return baseMapper.getMappingList();
    }

    @Override
    public CheckErrorTypeMappingVO getMappingBySourceError(String sourceCode, String sourceErrorCode) {
        return baseMapper.getMappingBySourceError(sourceCode, sourceErrorCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchMapping(List<CheckErrorTypeMapping> mappings) {
        return this.saveBatch(mappings);
    }
} 