package cn.dahe.service.impl;

import cn.dahe.common.constants.StatusConstants;
import cn.dahe.dao.WebsiteAccessRecordDao;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.WebsiteAccessOverviewDto;
import cn.dahe.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.WebsiteAccessRecord;
import cn.dahe.query.WebsiteAccessRecordQuery;
import cn.dahe.service.WebsiteAccessRecordService;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.NumberUtils;
import cn.dahe.utils.StringUtils;
import cn.dahe.utils.TimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 *
 */
@Service
@AllArgsConstructor
public class WebsiteAccessRecordServiceImpl extends BaseServiceImpl<WebsiteAccessRecordDao, WebsiteAccessRecord> implements WebsiteAccessRecordService {

    @Override
    public PageResult<WebsiteAccessRecord> page(WebsiteAccessRecordQuery query) {
        IPage<WebsiteAccessRecord> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public PageResult<WebsiteAccessOverviewDto> pageStats(WebsiteAccessRecordQuery query) {
        PageInfo<WebsiteAccessOverviewDto> objectPageInfo = PageHelper.startPage(query.getPage(), query.getLimit()).doSelectPageInfo(() ->
                baseMapper.listByFilters(ListUtils.transferIdsToList(query.getWebId()),
                        query.getGroupType(),
                        query.getBeginTime(), query.getEndTime()));
        return new PageResult<>(convertWebsiteAccessOverviewDto(objectPageInfo.getList()), objectPageInfo);
    }

    @Override
    public WebsiteUpdateStatsDto totalStats(WebsiteAccessRecordQuery query) {
        List<WebsiteAccessOverviewDto> overviewList = baseMapper.listByFilters(
                ListUtils.transferIdsToList(query.getWebId()),
                query.getGroupType(),
                query.getBeginTime(),
                query.getEndTime()
        );

        int totalSites = overviewList.size();

        // 按 errorCount 是否为0 分区：正常站点 & 出错站点
        Map<Boolean, List<WebsiteAccessOverviewDto>> partitioned = overviewList.stream()
                .collect(Collectors.partitioningBy(dto -> dto.getErrorCount() == 0));

        int normalSites = partitioned.getOrDefault(true, Collections.emptyList()).size();
        int errorSites = totalSites - normalSites;

        String errorRate = NumberUtils.calculateRatioPercentage(errorSites, totalSites, 2);

        WebsiteUpdateStatsDto statsDto = new WebsiteUpdateStatsDto();
        statsDto.setTotalSites(totalSites);
        statsDto.setNormalSites(normalSites);
        statsDto.setErrorSites(errorSites);
        statsDto.setErrorRate(errorRate);

        return statsDto;
    }


    private List<WebsiteAccessOverviewDto> convertWebsiteAccessOverviewDto(List<WebsiteAccessOverviewDto> list) {
        for (WebsiteAccessOverviewDto websiteAccessOverviewDto : list) {
            websiteAccessOverviewDto.setErrorRate(NumberUtils.calculateRatioPercentage(websiteAccessOverviewDto.getErrorCount(),
                    websiteAccessOverviewDto.getTotalCount(), 2));
        }
        return list;
    }


    private QueryWrapper<WebsiteAccessRecord> getWrapper(WebsiteAccessRecordQuery query) {
        QueryWrapper<WebsiteAccessRecord> queryWrapper = Wrappers.query();
        if (StringUtils.isNotBlank(query.getWebId())) {
            queryWrapper.in("web_id", ListUtils.transferIdsToList(query.getWebId()));
        }
        if (StringUtils.isNotBlank(query.getBeginTime())) {
            queryWrapper.ge("check_time", query.getBeginTime());
        }
        if (StringUtils.isNotBlank(query.getEndTime())) {
            queryWrapper.le("check_time", query.getEndTime());
        }
        queryWrapper.orderByDesc("check_time");
        return queryWrapper;
    }


}