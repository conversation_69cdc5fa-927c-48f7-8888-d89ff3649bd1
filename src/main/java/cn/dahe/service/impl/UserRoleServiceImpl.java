package cn.dahe.service.impl;

import cn.dahe.dao.RoleDao;
import cn.dahe.dao.UserRoleDao;
import cn.dahe.dto.Result;
import cn.dahe.entity.Role;
import cn.dahe.entity.UserRole;
import cn.dahe.service.UserRoleService;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@AllArgsConstructor
public class UserRoleServiceImpl extends BaseServiceImpl<UserRoleDao, UserRole> implements UserRoleService {

    @Resource
    private RoleDao roleDao;

    @Override
    public Result<String> save(UserRole userRole, LoginUserVO user) {
        baseMapper.insert(userRole);
        return Result.ok();
    }

    @Override
    public Result<String> update(String id, UserRole vo) {
        UserRole userRole = baseMapper.selectById(id);
        if (userRole == null) {
            return Result.error("请选择正确的数据");
        }
        boolean update = updateById(userRole);
        if (update) {
            return Result.ok();
        }
        return Result.error();
    }

    @Override
    public List<UserRole> listByUserId(Integer userId) {
        QueryWrapper<UserRole> wrapper = getWrapper(null, userId);
        return list(wrapper);
    }

    @Override
    public List<UserRole> listByRoleId(Integer roleId) {
        QueryWrapper<UserRole> wrapper = getWrapper(roleId, null);
        return list(wrapper);
    }

    @Override
    public Result removeByUserId(Integer userId) {
        QueryWrapper<UserRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return Result.ok(this.remove(queryWrapper));
    }

    @Override
    public Result saveByUserIdAndRoleIds(Integer userId, String roleIds) {
        List<String> collect = Arrays.stream(roleIds.split(",")).distinct().collect(Collectors.toList());
        for (String roleId : collect) {
            Role role = roleDao.selectById(roleId);
            if (role != null) {
                UserRole userRole = new UserRole();
                userRole.setRoleId(role.getId());
                userRole.setUserId(userId);
                this.save(userRole);
            }
        }
        return Result.ok();
    }


    private QueryWrapper<UserRole> getWrapper(Integer roleId, Integer userId) {
        QueryWrapper<UserRole> wrapper = Wrappers.query();
        if (userId != null) {
            wrapper.eq("user_id", userId);
        }
        if (roleId != null) {
            wrapper.eq("role_id", roleId);
        }
        return wrapper;
    }


}