package cn.dahe.service.impl;

import cn.dahe.dao.CheckErrorTypeDao;
import cn.dahe.entity.CheckErrorType;
import cn.dahe.service.CheckErrorTypeService;
import cn.dahe.vo.check.BaseErrorTypeVO;
import cn.dahe.vo.check.FirstLevelErrorTypeVO;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 错误类型Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
public class CheckErrorTypeServiceImpl extends BaseServiceImpl<CheckErrorTypeDao, CheckErrorType> implements CheckErrorTypeService {

    @PostConstruct
    private void postConstruct() {
        refreshErrorTypeNameCache();
    }

    @Override
    public List<FirstLevelErrorTypeVO> getTypeTree() {
        return baseMapper.getTypeTree();
    }

    @Override
    public List<CheckErrorType> getTypesByLevel(Integer level) {
        return this.lambdaQuery()
                .eq(CheckErrorType::getLevel, level)
                .orderByAsc(CheckErrorType::getSortOrder)
                .list();
    }

    @Override
    public List<CheckErrorType> getChildTypes(Long parentId) {
        return this.lambdaQuery()
                .eq(CheckErrorType::getParentId, parentId)
                .orderByAsc(CheckErrorType::getSortOrder)
                .list();
    }

    @Override
    public List<BaseErrorTypeVO> listTypeName() {
        return this.getBaseMapper().listTypeName();
    }

    /**
     * 刷新错误类型名称缓存
     */
    @Override
    public void refreshErrorTypeNameCache() {
        List<BaseErrorTypeVO> vos = this.listTypeName();
        TYPE_NAME_CACHE.clear();
        vos.forEach(vo -> TYPE_NAME_CACHE.put(vo.getId(), vo.getName()));
    }

} 