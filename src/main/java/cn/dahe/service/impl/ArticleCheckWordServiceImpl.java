package cn.dahe.service.impl;

import cn.dahe.dao.ArticleCheckWordDao;
import cn.dahe.entity.ArticleCheckWord;
import cn.dahe.service.ArticleCheckWordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 文章检查错误词库Service实现类
 */
@Service
public class ArticleCheckWordServiceImpl extends ServiceImpl<ArticleCheckWordDao, ArticleCheckWord> implements ArticleCheckWordService {

    @Resource
    private ArticleCheckWordDao articleCheckWordDao;

    /**
     * TODO 缓存
     *
     * @param errorWord         错误词
     * @param suggestWord       建议正确用词
     * @param errorTypeFirstId  一级错误类型ID
     * @param errorTypeSecondId 二级错误类型ID
     * @param errorTypeThirdId  三级错误类型ID
     * @param errorLevel        错误等级ID
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long getOrCreateCheckWord(String errorWord, String suggestWord,
                                     Long errorTypeFirstId, Long errorTypeSecondId,
                                     Long errorTypeThirdId, Long errorLevel) {
        // 1. 先查找是否已存在相同的错误词和建议词
        ArticleCheckWord word = articleCheckWordDao.selectByErrorWord(errorWord);
        if (word == null) {
            // 2. 如果不存在，创建新记录
            word = new ArticleCheckWord();
            word.setErrorWord(errorWord);
            word.setSuggestWord(suggestWord);
            word.setFirstErrorType(errorTypeFirstId);
            word.setSecondErrorType(errorTypeSecondId);
            word.setThirdErrorType(errorTypeThirdId);
            word.setErrorLevel(errorLevel);
            word.setIsFiltered(false);
            word.setCreateTime(new Date());
            word.setUpdateTime(new Date());
            this.save(word);
        }
        return word.getId();
    }

    @Override
    public ArticleCheckWord getById(Long id) {
        return articleCheckWordDao.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFilterStatus(Long id, Boolean isFiltered) {
        ArticleCheckWord error = new ArticleCheckWord();
        error.setId(id);
        error.setIsFiltered(isFiltered);
        error.setUpdateTime(new Date());
        return articleCheckWordDao.updateById(error) > 0;
    }
} 