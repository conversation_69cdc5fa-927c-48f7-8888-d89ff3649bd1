package cn.dahe.service.impl;

import cn.dahe.common.constants.StatusConstants;
import cn.dahe.dao.PermissionDao;
import cn.dahe.dao.RoleDao;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.dto.TreeNode;
import cn.dahe.entity.Permission;
import cn.dahe.entity.PermissionRole;
import cn.dahe.entity.Role;
import cn.dahe.query.PermissionQuery;
import cn.dahe.service.PermissionRoleService;
import cn.dahe.service.PermissionService;
import cn.dahe.utils.StringUtils;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@AllArgsConstructor
public class PermissionServiceImpl extends BaseServiceImpl<PermissionDao, Permission> implements PermissionService {

    @Resource
    private PermissionRoleService permissionRoleService;

    @Resource
    private RoleDao roleDao;


    @Override
    public PageResult<Permission> page(PermissionQuery query) {
        IPage<Permission> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    private QueryWrapper<Permission> getWrapper(PermissionQuery query) {
        QueryWrapper<Permission> wrapper = Wrappers.query();
        if (StringUtils.isNotBlank(query.getKeyword())) {
            wrapper.like("name", query.getKeyword());
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            wrapper.eq("status", query.getStatus());
        }
        if (StringUtils.isNotBlank(query.getPid())) {
            wrapper.eq("pid", query.getPid());
        }
        wrapper.orderByDesc("id");
        return wrapper;
    }

    @Override
    public Result<String> save(Permission permission, LoginUserVO user) {
        String errorMsg = checkPermission(permission);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(errorMsg);
        }
        Permission entity = new Permission();
        entity.setNote(StringUtils.defaultIfBlank(permission.getNote(), ""));
        entity.setName(permission.getName());
        entity.setPid(permission.getPid());
        entity.setStatus(StatusConstants.COMMON_NORMAL);
        entity.setType(permission.getType());
        entity.setSn(permission.getSn());
        if (!this.save(permission)) {
            return Result.error();
        }
        return Result.ok();
    }


    private String checkPermission(Permission permission) {
        if (StringUtils.isBlank(permission.getName())) {
            return "请填写权限名称";
        }
        if (StringUtils.isBlank(permission.getSn())) {
            return "请填写权限标识符";
        }
        List<Permission> bySn = getBySn(permission.getSn(), permission.getId());
        if (!bySn.isEmpty()) {
            return "权限标识符重复";
        }
        if (permission.getType() < 0) {
            return "请选择权限类型";
        }
        if (permission.getPid() < 0) {
            return "请选择权限类型";
        }
        return "";
    }

    public List<Permission> getBySn(String sn, Integer id) {
        QueryWrapper<Permission> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sn", sn);
        queryWrapper.eq("status", StatusConstants.COMMON_NORMAL);
        if (id != null) {
            queryWrapper.ne("id", id);
        }
        return list(queryWrapper);
    }


    @Override
    public Result<String> update(String id, Permission vo) {
        Permission permission = baseMapper.selectById(id);
        if (permission == null) {
            return Result.error("请选择正确的数据");
        }
        vo.setId(permission.getId());
        String errorMsg = checkPermission(vo);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(errorMsg);
        }
        permission.setNote(StringUtils.defaultIfBlank(vo.getNote(), ""));
        permission.setName(vo.getName());
        permission.setPid(vo.getPid());
        permission.setType(vo.getType());
        permission.setSn(vo.getSn());
        if (!updateById(permission)) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateStatus(String id) {
        Permission permission = baseMapper.selectById(id);
        if (permission == null) {
            return Result.error("请选择正确的数据");
        }
        List<Permission> bySn = getBySn(permission.getSn(), permission.getId());
        if (!bySn.isEmpty()) {
            return  Result.error("权限标识符重复");
        }
        permission.setStatus(permission.getStatus() == StatusConstants.COMMON_NORMAL ? StatusConstants.COMMON_DISABLE : StatusConstants.COMMON_NORMAL);
        if (!updateById(permission)) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public List<TreeNode> getTree() {
        List<Permission> permissions = listByStatus(StatusConstants.COMMON_NORMAL);
        return buildTree(0, permissions);
    }

    @Override
    public List<TreeNode> getRoleTree(Integer roleId) {
        List<Permission> permissions = listByStatus(StatusConstants.COMMON_NORMAL);
        List<PermissionRole> permissionRoles = permissionRoleService.listByRoleId(roleId);
        //查找当前角色拥有的权限
        List<Integer> rolePermissionIds = permissionRoles.stream().map(PermissionRole::getPermissionId).distinct().collect(Collectors.toList());
        //开始构建用户角色树
        return buildCheckedTree(0, permissions, rolePermissionIds);
    }

    @Override
    public List<Permission> listByRoleIds(Collection<Integer> roleIds) {
        List<Permission> list = new ArrayList<>();
        for (Integer roleId : roleIds) {
            List<Integer> permissionIds = permissionRoleService.listByRoleId(roleId)
                    .stream()
                    .map(PermissionRole::getPermissionId)
                    .collect(Collectors.toList());
            if (permissionIds.isEmpty()) {
                continue;
            }
            List<Permission> permissions = this.listByIds(permissionIds);
            list.addAll(permissions);
        }
        return list;
    }

    @Override
    public List<Permission> listByRoleSns(Collection<String> roleSns) {
        Set<Permission> permissionSet = new HashSet<>();
        List<Role> roleList = roleDao.listByFilters(null, new ArrayList<>(roleSns), String.valueOf(StatusConstants.COMMON_NORMAL));

        for (Role role : roleList) {
            List<Permission> rolePermissions = role.getPermissionArrayList();
            permissionSet.addAll(rolePermissions);
        }

        return new ArrayList<>(permissionSet);
    }


    private List<Permission> listByStatus(Integer status) {
        QueryWrapper<Permission> queryWrapper = new QueryWrapper<>();
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        return list(queryWrapper);
    }

    private static List<TreeNode> buildTree(int id, List<Permission> permissions) {
        List<TreeNode> list = new ArrayList<>();
        for (Permission permission : permissions) {
            if (permission.getPid() == id) {
                TreeNode treeNode = new TreeNode();
                treeNode.setId(String.valueOf(permission.getId()));
                treeNode.setName(permission.getName());
                treeNode.setPid(String.valueOf(permission.getPid()));
                treeNode.setChildrenNode(buildTree(permission.getId(), permissions));
                list.add(treeNode);
            }
        }
        return list;
    }

    private static List<TreeNode> buildCheckedTree(int id, List<Permission> permissions, List<Integer> rolePermissionIds) {
        List<TreeNode> list = new ArrayList<>();
        for (Permission permission : permissions) {
            if (permission.getPid() == id) {
                TreeNode treeNode = new TreeNode();
                treeNode.setId(String.valueOf(permission.getId()));
                treeNode.setName(permission.getName());
                treeNode.setPid(String.valueOf(permission.getPid()));
                treeNode.setChecked(rolePermissionIds.contains(permission.getId()));
                treeNode.setChildrenNode(buildCheckedTree(permission.getId(), permissions, rolePermissionIds));
                list.add(treeNode);
            }
        }
        return list;
    }


}