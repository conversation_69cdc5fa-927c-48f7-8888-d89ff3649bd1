package cn.dahe.service.impl;

import cn.dahe.dao.ArticleCheckStatsDao;
import cn.dahe.query.ArticleCheckStatQuery;
import cn.dahe.query.ArticleCheckWordStatQuery;
import cn.dahe.service.ArticleCheckStatsService;
import cn.dahe.vo.WebsiteArticleCheckStatsVO;
import cn.dahe.vo.WebsiteArticleCheckTotalStatsVO;
import cn.dahe.vo.WebsiteArticleCheckWordStatsVO;
import cn.dahe.vo.WebsiteArticleCheckWordTotalStatsVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 网站文章错误统计服务实现类
 */
@Service
public class ArticleCheckStatsServiceImpl implements ArticleCheckStatsService {
    @Resource
    private ArticleCheckStatsDao articleCheckStatsDao;

    @Override
    public IPage<WebsiteArticleCheckStatsVO> pageStatsByWebsite(ArticleCheckStatQuery query) {
        return articleCheckStatsDao.pageStatsByWebsite(Page.of(query.getPage(), query.getLimit()), query);
    }

    @Override
    public WebsiteArticleCheckTotalStatsVO queryTotalStats(ArticleCheckStatQuery query) {
        return articleCheckStatsDao.queryTotalStats(query);
    }

    @Override
    public WebsiteArticleCheckWordTotalStatsVO queryWordTotalStats(ArticleCheckWordStatQuery query) {
        return articleCheckStatsDao.queryWordTotalStats(query);
    }

    @Override
    public IPage<WebsiteArticleCheckWordStatsVO> pageWordStatsByWebsite(ArticleCheckWordStatQuery query) {
        return articleCheckStatsDao.pageWordStatsByWebsite(Page.of(query.getPage(), query.getLimit()), query);
    }


} 