package cn.dahe.service.impl;

import cn.dahe.dao.ChkAttachUrlDao;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.ChkAttachUrl;
import cn.dahe.query.ChkAttachUrlQuery;
import cn.dahe.service.ChkAttachUrlService;
import cn.dahe.vo.ChkAttachUrlVO;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 附件检查Service实现类 - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
public class ChkAttachUrlServiceImpl extends BaseServiceImpl<ChkAttachUrlDao, ChkAttachUrl> implements ChkAttachUrlService {

    // ==================== 附件检查记录 ====================

    @Override
    public PageResult<ChkAttachUrlVO> page(ChkAttachUrlQuery query) {
        try {
            Page<ChkAttachUrlVO> page = new Page<>(query.getPage(), query.getLimit());
            IPage<ChkAttachUrlVO> result = baseMapper.selectPageWithExtInfo(page, query);
            return PageResult.page(result);
        } catch (Exception e) {
            log.error("查询附件检查记录失败，返回兜底数据", e);
        }
        return PageResult.page(new Page<>());
    }

    @Override
    public ChkAttachUrlVO get(Long id) {
        try {
            ChkAttachUrlVO result = baseMapper.selectDetailById(id);
            return result;
        } catch (Exception e) {
            log.error("获取附件检查详情失败，返回兜底数据", e);
            return null;
        }
    }

    // ==================== 数据导出 ====================

    @Override
    public void export(ChkAttachUrlQuery query, LoginUserVO user, HttpServletResponse response) throws IOException {
        try {
            log.info("用户{}导出附件检查记录", user.getUserId());

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("附件检查记录_" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 获取数据
            query.setPage(1);
            query.setLimit(10000); // 导出最多10000条
            PageResult<ChkAttachUrlVO> pageResult = this.page(query);
            List<ChkAttachUrlVO> dataList = pageResult.getList();

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("附件检查记录");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"序号", "网站名称", "来源网站", "来源页面", "发布时间", "检测时间", "附件名称", "附件类型", "检测状态"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // 填充数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                ChkAttachUrlVO data = dataList.get(i);

                row.createCell(0).setCellValue(i + 1);
                row.createCell(1).setCellValue(data.getWebsiteName() != null ? data.getWebsiteName() : "");
                row.createCell(2).setCellValue(data.getSourceWebsite() != null ? data.getSourceWebsite() : "");
                row.createCell(3).setCellValue(data.getSourcePage() != null ? data.getSourcePage() : "");
                row.createCell(4).setCellValue(data.getPublishTime() != null ?
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(data.getPublishTime()) : "");
                row.createCell(5).setCellValue(data.getCheckTime() != null ?
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(data.getCheckTime()) : "");
                row.createCell(6).setCellValue(data.getAttachName() != null ? data.getAttachName() : "");
                row.createCell(7).setCellValue(data.getAttachType() != null ? data.getAttachType() : "");
                row.createCell(8).setCellValue(data.getCheckStatus() != null ? data.getCheckStatus() : "");
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("导出附件检查记录失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }

}
