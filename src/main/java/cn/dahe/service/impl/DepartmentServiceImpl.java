package cn.dahe.service.impl;


import cn.dahe.common.constants.StatusConstants;
import cn.dahe.dao.DepartmentDao;
import cn.dahe.dao.DictDao;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.*;

import cn.dahe.query.DepartmentQuery;
import cn.dahe.service.DepartmentService;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.StringUtils;
import cn.dahe.vo.DepartmentVO;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 *
 */
@Service("departmentService")
@AllArgsConstructor
public class DepartmentServiceImpl extends BaseServiceImpl<DepartmentDao, Department> implements DepartmentService {

    @Resource
    private DictDao dictDao;


    @Override
    public PageResult page(DepartmentQuery query) {
        Page<Department> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult(convertListToDto(dataPage.getRecords()), dataPage);
    }

    @Override
    public List<DepartmentVO> getByStatusAndName(String name, Integer status) {
        QueryWrapper<Department> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", StatusConstants.COMMON_NORMAL);
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like("name", name);
        }
        //seq越大越靠后
        queryWrapper.orderByAsc("type");
        queryWrapper.orderByDesc("seq");
        return convertListToDto(list(queryWrapper));
    }

    @Override
    public Result<String> save(DepartmentVO vo, LoginUserVO loginUserVO) {
        String errorMsg = checkDepatmentVO(vo);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(errorMsg);
        }
        Department department = new Department();
        BeanUtils.copyProperties(vo, department);
        department.setType(Integer.parseInt(vo.getType()));
        department.setCreateTime(new Date());
        department.setUpdateTime(department.getCreateTime());
        department.setUpdateUserId(loginUserVO.getUserId());
        department.setUpdateUserName(loginUserVO.getUsername());
        if (!this.save(department)) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> update(String id, DepartmentVO vo, LoginUserVO loginUserVO) {
        Department department = this.getById(id);
        if (department == null) {
            return Result.error("请传入正确的ID");
        }
        String errorMsg = checkDepatmentVO(vo);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(errorMsg);
        }
        department.setSeq(vo.getSeq() == null ? 0 : vo.getSeq());
        department.setType(Integer.parseInt(vo.getType()));
        department.setDictProjects(vo.getDictProjects());
        department.setName(vo.getName());
        department.setUpdateTime(new Date());
        department.setUpdateUserId(loginUserVO.getUserId());
        department.setUpdateUserName(loginUserVO.getUsername());
        if (!this.updateById(department)) {
            return Result.error();
        }
        return Result.ok();
    }

    private DepartmentVO convertDepartmentToDto(Department department) {
        DepartmentVO departmentVO = new DepartmentVO();
        if (department != null) {
            BeanUtils.copyProperties(department, departmentVO);
            departmentVO.setType(String.valueOf(department.getType()));
            String dictProjectsStr = getDictProjectsStr(department.getDictProjects());
            departmentVO.setDictProjectsStr(dictProjectsStr);
        }
        return departmentVO;
    }

    private List<DepartmentVO> convertListToDto(List<Department> departmentList) {
        List<DepartmentVO> list = new ArrayList<>();
        if (!departmentList.isEmpty()) {
            for (Department department : departmentList) {
                DepartmentVO departmentVO = convertDepartmentToDto(department);
                list.add(departmentVO);
            }
        }
        return list;
    }

    private String getDictProjectsStr(String dictProjects) {
        if (StringUtils.isNotBlank(dictProjects)) {
            List<String> projectIds = ListUtils.transferIdsToList(dictProjects);
            if (!projectIds.isEmpty()) {
                QueryWrapper<Dict> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("id", projectIds);
                queryWrapper.orderByDesc("seq");
                List<Dict> projects = dictDao.selectList(queryWrapper);
                return projects.stream().map(Dict::getName).collect(Collectors.joining(","));
            }
        }
        return "";
    }

    private QueryWrapper<Department> getWrapper(DepartmentQuery query) {
        QueryWrapper<Department> wrapper = Wrappers.query();
        if (StringUtils.isNotBlank(query.getName())) {
            wrapper.like("name", query.getName());
        }
        if (StringUtils.isNotBlank(query.getType())) {
            wrapper.eq("type", query.getType());
        }
        //seq越大越靠后
        wrapper.orderByAsc("type");
        wrapper.orderByDesc("seq");
        return wrapper;
    }

    private String checkDepatmentVO(DepartmentVO departmentVO) {
        if (StringUtils.isBlank(departmentVO.getName())) {
            return "单位名称不能为空";
        }
        if (StringUtils.isBlank(departmentVO.getType())) {
            return "请选择单位类型";
        }

        List<String> dictProjects = ListUtils.transferIdsToList(departmentVO.getDictProjects());
        if (dictProjects == null || dictProjects.isEmpty()) {
            return "请选择参评项目";
        }

        List<Dict> dicts = dictDao.selectList(new QueryWrapper<Dict>().in("id", dictProjects));
        if (dicts.isEmpty()) {
            return "请选择参评项目";
        }

        String collect = dicts.stream()
                .sorted(Comparator.comparingInt(Dict::getSeq).reversed())
                .map(dict -> String.valueOf(dict.getId()))
                .distinct()
                .collect(Collectors.joining(","));
        departmentVO.setDictProjects(collect);
        return "";
    }


}