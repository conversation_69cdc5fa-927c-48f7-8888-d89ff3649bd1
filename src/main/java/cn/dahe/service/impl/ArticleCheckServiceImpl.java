package cn.dahe.service.impl;

import cn.dahe.dao.ArticleCheckDao;
import cn.dahe.dto.*;
import cn.dahe.entity.ArticleCheck;
import cn.dahe.entity.ArticleCheckContent;
import cn.dahe.entity.Website;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.query.ArticleCheckQuery;
import cn.dahe.query.ArticleCheckStatQuery;
import cn.dahe.service.*;
import cn.dahe.utils.HtmlTextMapper;
import cn.dahe.utils.HttpUtil;
import cn.dahe.utils.SpringUtils;
import cn.dahe.vo.check.ArticleCheckVO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Element;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.SocketTimeoutException;
import java.util.*;

/**
 * 内容错误详情Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
public class ArticleCheckServiceImpl extends BaseServiceImpl<ArticleCheckDao, ArticleCheck> implements ArticleCheckService {


    @Resource
    private WebsiteService websiteService;

    @Resource
    private ArticleCheckContentService articleCheckContentService;

    @Resource
    private ArticleService articleService;

    @Override
    public Boolean updateErrorAuditStatus(Long articleId, List<Long> checkIds, AuditStatusEnum auditStatus) {
        //  TODO 检查checkIds和articleId的关系
        // LoginUserVO loginUser = SecurityUtils.getLoginUser();
        LambdaUpdateWrapper<ArticleCheck> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ArticleCheck::getAuditStatus, auditStatus.getType());
        // if (auditStatus != AuditStatusEnum.WAITING_FOR_REVIEW){
        //     updateWrapper.set(ArticleCheck::getAuditUserId, loginUser.getUsername());
        //     updateWrapper.set(ArticleCheck::getAuditTime, new Date());
        // }
        updateWrapper.in(ArticleCheck::getId, checkIds);
        return this.update(updateWrapper);
    }


    @Override
    public PageResult<ArticleErrorStatsDto> pageWebsiteErrorStatistics(ArticleCheckStatQuery query) {
        PageResult<Website> websitePageResult = websiteService.pageByWebIds(query.getWebsiteIds(), query.getPage(), query.getLimit());
        List<ArticleErrorStatsDto> dtoArrayList = new ArrayList<>();
        for (Website website : websitePageResult.getList()) {
            // 错误的文章数量
            ArticleErrorStatsDto articleErrorStatsDto = baseMapper.countErrorArticleStats(
                    CollUtil.newArrayList(website.getId()),
                    query.getErrorLevels(),
                    query.getPubBeginDate(),
                    query.getPubEndDate());
            if (articleErrorStatsDto == null) {
                articleErrorStatsDto = new ArticleErrorStatsDto();
            }
            articleErrorStatsDto.setWebId(website.getId());
            articleErrorStatsDto.setWebSiteName(website.getWebName());

            dtoArrayList.add(articleErrorStatsDto);
        }
        return new PageResult<>(dtoArrayList, websitePageResult.getTotal(), websitePageResult.getPageSize(), websitePageResult.getPage());

    }


    @Override
    public List<ArticleCheckVO> getArticleCheckList(Long articleId, ArticleCheckQuery query) {
        // 获取DTO列表
        List<ArticleCheckVO> results = this.getBaseMapper().getArticleCheckList(articleId, query);

        return results;
    }


    @Override
    public ArticleCheckContentDto initPushArticle(Long articleId, String title, String content) {
        // 1. 清理HTML
        String htmlTitle = HtmlTextMapper.cleanHtml(title);
        String htmlContent = HtmlTextMapper.cleanHtml(content);

        // 2. 提取文本
        HtmlTextMapper.HtmlResult titleResult = HtmlTextMapper.extractText(htmlTitle);
        HtmlTextMapper.HtmlResult contentResult = HtmlTextMapper.extractText(htmlContent);

        // 3. 保存处理后的内容
        ArticleCheckContent articleCheckContent = new ArticleCheckContent();
        articleCheckContent.setArticleId(articleId);
        articleCheckContent.setCompressedTitle(titleResult.getCompressedHtml());
        articleCheckContent.setCompressedContent(contentResult.getCompressedHtml());
        articleCheckContent.setCleanedTitle(titleResult.getPlainText());
        articleCheckContent.setCleanedContent(contentResult.getPlainText());
        articleCheckContent.setCheckStatus(0);
        articleCheckContentService.save(articleCheckContent);

        return new ArticleCheckContentDto().setArticleContentId(articleCheckContent.getId()).setCleanedContent(contentResult.getPlainText()).setCleanedTitle(titleResult.getPlainText())
                .setTitleResult(titleResult).setContentResult(contentResult);
    }


    @Value("${content-check.api.url}")
    private String checkApiUrl;

    @Value("${content-check.api.timeout}")
    private int apiTimeout;

    @Value("${spring.profiles.active}")
    private String env;
    @Resource
    private SimCheckService simCheckService;

    @Override
    public ContentCheckResultDto executeContentCheck(String title, String content) {
        if (env.equals("dev")) {
            long startTime = System.currentTimeMillis();
            List<ArticleCheck> articleChecks = simCheckService.simulateCheck(title, content);
            long responseTime = System.currentTimeMillis() - startTime;
            return ContentCheckResultDto.success(articleChecks, responseTime);
        }
        // 构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("title", title);
        params.put("content", content);

        long startTime = System.currentTimeMillis();
        try {
            // 使用HttpUtil发送请求
            String response = HttpUtil.post(checkApiUrl, params, apiTimeout);
            long responseTime = System.currentTimeMillis() - startTime;

            // 解析响应
            try {
                Result<?> result = JSON.parseObject(response, Result.class);
                // 检查响应状态
                if (result == null) {
                    return ContentCheckResultDto.apiError("接口返回为空", null);
                }
                if (!result.isSuccess()) {
                    return ContentCheckResultDto.apiError(
                            result.getMsg(),
                            String.format("错误码：%s，响应信息：%s", result.getCode(), result.getMsg())
                    );
                }
                JSONObject jsonObject = JSON.parseObject(response);
                List<ArticleCheck> checks = jsonObject.getJSONArray("data").toJavaList(ArticleCheck.class);
                // 返回成功结果
                return ContentCheckResultDto.success(checks, responseTime);
            } catch (Exception e) {
                return ContentCheckResultDto.apiError(
                        "响应解析失败",
                        StrUtil.format("异常信息：{}，响应数据：\n{}", e.getMessage(), response)
                );
            }
        } catch (SocketTimeoutException e) {
            log.error("调用校对服务超时", e);
            return ContentCheckResultDto.timeoutError(
                    "调用校对服务超时",
                    String.format("请求耗时超过%d毫秒", apiTimeout)
            );
        } catch (Exception e) {
            log.error("调用校对服务失败", e);
            return ContentCheckResultDto.localError(
                    "调用校对服务失败",
                    ExceptionUtil.stacktraceToString(e)
            );
        }
    }

    @Resource
    private ArticleCheckWordService articleCheckWordService;

    // 缓存分句结果
    private List<HtmlTextMapper.SentenceLocation> titleSentences;
    private List<HtmlTextMapper.SentenceLocation> contentSentences;

    @Override
    public void processCheckResults(Long articleId, ArticleCheckContentDto checkContentDto, ContentCheckResultDto checkResult) {
        Long articleContentId = checkContentDto.getArticleContentId();
        if (checkResult.isSuccess()) {
            List<ArticleCheck> checkResults = checkResult.getCheckResults();
            HtmlTextMapper.HtmlResult titleResult = checkContentDto.getTitleResult();
            HtmlTextMapper.HtmlResult contentResult = checkContentDto.getContentResult();
            // 获取或创建分句列表（根据位置复用）
            List<HtmlTextMapper.SentenceLocation> titleSentences = HtmlTextMapper.splitSentences(checkContentDto.getCleanedTitle());
            List<HtmlTextMapper.SentenceLocation> contentSentences = HtmlTextMapper.splitSentences(checkContentDto.getCleanedContent());
            // 更新校对结果的位置信息
            if (CollUtil.isNotEmpty(checkResults)) {
                for (ArticleCheck check : checkResults) {
                    check.setArticleId(articleId);
                    String errorWord = check.getErrorWord();
                    String suggestWord = check.getSuggestWord();
                    Long checkWordId = articleCheckWordService.getOrCreateCheckWord(errorWord, suggestWord, check.getFirstErrorTypeId(), check.getSecondErrorTypeId(), check.getThirdErrorTypeId(), check.getErrorLevel());
                    check.setWordId(checkWordId);
                    // 根据是标题还是正文，使用对应的映射关系更新HTML位置
                    HtmlTextMapper.HtmlWordLocation location;
                    if (check.getArticleLocation() == 1) { // 标题
                        location = HtmlTextMapper.findInHtml(errorWord, check.getPosition(), titleResult);
                        if (location != null) {
                            check.setHtmlPosition(location.getStartPosition());
                            check.setHtmlErrorWord(location.getHtmlErrorWord());
                        }
                    } else { // 正文
                        location = HtmlTextMapper.findInHtml(errorWord, check.getPosition(), contentResult);
                        if (location != null) {
                            check.setHtmlPosition(location.getStartPosition());
                            check.setHtmlErrorWord(location.getHtmlErrorWord());
                        }
                    }

                    // 获取或创建分句列表（根据位置复用）
                    HtmlTextMapper.MarkedErrorContext markedContext;
                    if (check.getArticleLocation() == 1) {
                        markedContext = getMarkedErrorContext(titleSentences, check);
                    } else {
                        markedContext = getMarkedErrorContext(contentSentences, check);
                    }
                    if (markedContext != null) {
                        check.setContext(markedContext.getContext());
                        check.setMarkedContext(markedContext.getMarkedContext());
                    }

                    check.setCreateTime(new Date());
                    check.setAuditStatus(0); // 未审核
                }
                SpringUtils.getBean(ArticleCheckService.class).saveBatch(checkResults);
            }
            //  更新文章表数据
            articleService.updateCheckInfo(articleId, articleContentId);
        }
        // 更新校对状态和结果
        articleCheckContentService.updateCheckContentStatus(articleContentId, checkResult);
    }

    /**
     * 获取并标记错词的上下文
     *
     * @param sentences 已分好的句子列表
     * @return 包含原始上下文和标记后上下文的对象
     */
    public static HtmlTextMapper.MarkedErrorContext getMarkedErrorContext(List<HtmlTextMapper.SentenceLocation> sentences, ArticleCheck checkResult) {
        String errorWord = checkResult.getErrorWord();
        int position = checkResult.getPosition();
        // 1. 获取错词上下文
        HtmlTextMapper.ErrorWordContext errorContext = HtmlTextMapper.getErrorWordContext(sentences, position, errorWord.length());
        if (errorContext == null) {
            return null;
        }
        // 2. 在上下文中标记错词
        StringBuilder context = new StringBuilder(errorContext.getContext());
        int relativePos = errorContext.getRelativePosition();
        Element errorRemarkSpan = new Element("span")
                .attr("class", StrUtil.format("sensitive_lv{}_remark", checkResult.getErrorLevel()));
        String remarkText = StrUtil.format("{}({};{};{})", errorWord, CheckErrorLevelService.getLevelName(checkResult.getErrorLevel().intValue()),
                CheckErrorTypeService.getTypeName(checkResult.getThirdErrorTypeId() == null ? checkResult.getSecondErrorTypeId() : checkResult.getThirdErrorTypeId()),
                StrUtil.isBlankIfStr(checkResult.getSuggestWord()) ? "" : StrUtil.format("建议改为:{};", checkResult.getSuggestWord()));
        String errorRemarkTag = errorRemarkSpan.text(remarkText).outerHtml();
        String markedContext = context.replace(relativePos, relativePos + errorWord.length(), errorRemarkTag).toString();

        return new HtmlTextMapper.MarkedErrorContext(
                errorContext.getContext(),
                markedContext,
                errorContext.getContextStart(),
                errorContext.getContextEnd()
        );
    }


}