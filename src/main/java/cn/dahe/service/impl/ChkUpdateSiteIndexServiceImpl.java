package cn.dahe.service.impl;

import cn.dahe.dao.ChkUpdateSiteIndexDao;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.ChkUpdateSiteIndex;
import cn.dahe.query.ChkUpdateSiteIndexQuery;
import cn.dahe.service.ChkUpdateSiteIndexService;
import cn.dahe.vo.ChkUpdateSiteIndexVO;
import cn.dahe.vo.LoginUserVO;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首页更新检查Service实现类 - 专注于首页更新检查功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
public class ChkUpdateSiteIndexServiceImpl extends BaseServiceImpl<ChkUpdateSiteIndexDao, ChkUpdateSiteIndex> implements ChkUpdateSiteIndexService {

    // ==================== 首页更新检查概览 ====================

    @Override
    public Map<String, Object> getOverviewStatistics(ChkUpdateSiteIndexQuery query) {
        try {
            Map<String, Object> result = baseMapper.getOverviewStatistics(query);
            
            // 如果没有数据，返回兜底数据
            if (result == null || result.isEmpty()) {
                return createMockOverviewStatistics();
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取网站概览统计失败，返回兜底数据", e);
            return createMockOverviewStatistics();
        }
    }

    // ==================== 网站详情列表 ====================

    @Override
    public PageResult<ChkUpdateSiteIndexVO> page(ChkUpdateSiteIndexQuery query) {
        try {
            Page<ChkUpdateSiteIndexVO> page = new Page<>(query.getPage(), query.getLimit());
            
            IPage<ChkUpdateSiteIndexVO> result = baseMapper.selectPageWithExtInfo(
                    page,
                    StrUtil.isNotBlank(query.getGroupName()) ? query.getGroupName() : null,
                    StrUtil.isNotBlank(query.getWebsiteName()) ? query.getWebsiteName() : null,
                    StrUtil.isNotBlank(query.getWebsiteIndexUrl()) ? query.getWebsiteIndexUrl() : null,
                    StrUtil.isNotBlank(query.getParseBeginTime()) ? query.getParseBeginTime() : null,
                    StrUtil.isNotBlank(query.getParseEndTime()) ? query.getParseEndTime() : null,
                    StrUtil.isNotBlank(query.getCreateBeginTime()) ? query.getCreateBeginTime() : null,
                    StrUtil.isNotBlank(query.getCreateEndTime()) ? query.getCreateEndTime() : null
            );
            
            // 如果没有数据，返回兜底数据
            if (result.getRecords().isEmpty()) {
                return createMockPageData(query);
            }
            
            return PageResult.page(result);
        } catch (Exception e) {
            log.error("查询网站详情列表失败，返回兜底数据", e);
            return createMockPageData(query);
        }
    }

    @Override
    public ChkUpdateSiteIndexVO get(Long id) {
        try {
            ChkUpdateSiteIndexVO result = baseMapper.selectDetailById(id);
            if (result == null) {
                // 返回兜底数据
                return createMockDetailData(id);
            }
            return result;
        } catch (Exception e) {
            log.error("获取网站详情失败，返回兜底数据", e);
            return createMockDetailData(id);
        }
    }


    // ==================== 数据导出 ====================

    @Override
    public void export(ChkUpdateSiteIndexQuery query, LoginUserVO user, HttpServletResponse response) throws IOException {
        try {
            log.info("用户{}导出首页更新检查记录", user.getUserId());

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("首页更新检查记录_" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 获取数据
            query.setPage(1);
            query.setLimit(10000); // 导出最多10000条
            PageResult<ChkUpdateSiteIndexVO> pageResult = this.page(query);
            List<ChkUpdateSiteIndexVO> dataList = pageResult.getList();

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("首页更新检查记录");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"序号", "网站名称", "网站地址", "首页是否更新", "更新天数", "连续未更新天数", "最后一次更新时间"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // 填充数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                ChkUpdateSiteIndexVO data = dataList.get(i);

                row.createCell(0).setCellValue(i + 1);
                row.createCell(1).setCellValue(data.getWebsiteName() != null ? data.getWebsiteName() : "");
                row.createCell(2).setCellValue(data.getWebsiteIndexUrl() != null ? data.getWebsiteIndexUrl() : "");
                row.createCell(3).setCellValue(data.getIsUpdated() != null ? data.getIsUpdated() : "");
                row.createCell(4).setCellValue(data.getUpdateDays() != null ? data.getUpdateDays() : 0);
                row.createCell(5).setCellValue(data.getContinuousNotUpdateDays() != null ? data.getContinuousNotUpdateDays() : 0);
                row.createCell(6).setCellValue(data.getLastUpdateTime() != null ?
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(data.getLastUpdateTime()) : "");
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("导出首页更新检查记录失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }

    // ==================== 兜底数据创建方法 ====================

    /**
     * 创建概览统计兜底数据
     */
    private Map<String, Object> createMockOverviewStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalWebsiteCount", 100);  // 检测网站数
        statistics.put("updatedWebsiteCount", 37); // 更新网站
        statistics.put("notUpdatedWebsiteCount", 63); // 未更新网站
        return statistics;
    }

    /**
     * 创建分页数据兜底数据
     */
    private PageResult<ChkUpdateSiteIndexVO> createMockPageData(ChkUpdateSiteIndexQuery query) {
        List<ChkUpdateSiteIndexVO> mockList = new ArrayList<>();

        String[] websites = {"河南师范大学", "中原工学院", "河南理工大学"};
        String[] urls = {"https://www.henan.edu.cn/", "https://www.zut.edu.cn/", "https://www.hpu.edu.cn/"};
        String[] isUpdatedArray = {"是", "否", "是"};

        int limit = query.getLimit() != null ? query.getLimit() : 10;
        for (int i = 1; i <= Math.min(limit, 3); i++) {
            ChkUpdateSiteIndexVO vo = new ChkUpdateSiteIndexVO();
            vo.setId((long) (i + 5)); // 从6开始，对应原型图
            vo.setSerialNumber(i + 5); // 序号
            vo.setWebsiteName(websites[i - 1]); // 网站名称
            vo.setWebsiteIndexUrl(urls[i - 1]); // 网站地址
            vo.setIsUpdated(isUpdatedArray[i - 1]); // 首页是否更新
            vo.setUpdateDays(i * 2); // 更新天数
            vo.setContinuousNotUpdateDays(i == 2 ? 5 : 0); // 连续未更新天数

            // 最后一次更新时间
            vo.setLastUpdateTime(new Date(System.currentTimeMillis() - i * 24 * 60 * 60 * 1000L));

            vo.setOperation("内容明细"); // 操作
            vo.setGroupName("高校网站"); // 分组名称
            vo.setWebsiteId((long) i); // 网站ID
            vo.setUpdateStatus("正常"); // 更新状态
            vo.setCheckStatus("已检查"); // 检查状态

            mockList.add(vo);
        }

        return new PageResult<>(mockList, 3L, limit, query.getPage() != null ? query.getPage() : 1);
    }

    /**
     * 创建详情兜底数据
     */
    private ChkUpdateSiteIndexVO createMockDetailData(Long id) {
        ChkUpdateSiteIndexVO vo = new ChkUpdateSiteIndexVO();
        vo.setId(id);
        vo.setSerialNumber(6); // 序号
        vo.setWebsiteName("河南师范大学"); // 网站名称
        vo.setWebsiteIndexUrl("https://www.henan.edu.cn/"); // 网站地址
        vo.setIsUpdated("是"); // 首页是否更新
        vo.setUpdateDays(3); // 更新天数
        vo.setContinuousNotUpdateDays(0); // 连续未更新天数
        vo.setLastUpdateTime(new Date()); // 最后一次更新时间
        vo.setOperation("内容明细"); // 操作
        vo.setGroupName("高校网站"); // 分组名称
        vo.setWebsiteId(1L); // 网站ID
        vo.setUpdateStatus("正常"); // 更新状态
        vo.setCheckStatus("已检查"); // 检查状态
        return vo;
    }

}
