package cn.dahe.service.impl;

import cn.dahe.dao.ArticleCheckContentDao;
import cn.dahe.dao.ArticleCheckDao;
import cn.dahe.dto.ContentCheckResultDto;
import cn.dahe.entity.ArticleCheckContent;
import cn.dahe.query.ArticleCheckQuery;
import cn.dahe.service.*;
import cn.dahe.vo.check.ArticleCheckVO;
import cn.dahe.vo.check.ArticleContentCheckVO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文章检查内容服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class ArticleCheckContentServiceImpl extends ServiceImpl<ArticleCheckContentDao, ArticleCheckContent> implements ArticleCheckContentService {
    @Resource
    private CheckErrorTypeService checkErrorTypeService;
    @Resource
    private CheckErrorLevelService checkErrorLevelService;
    @Resource
    private ArticleContentService articleContentService;
    @Resource
    private ArticleService articleService;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private ArticleCheckDao articleCheckDao;

    @Override
    public ArticleCheckContent getByArticleId(Long articleId) {
        LambdaQueryWrapper<ArticleCheckContent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ArticleCheckContent::getArticleId, articleId);
        return getOne(wrapper);
    }


    @Override
    public IPage<ArticleContentCheckVO> pageArticleChecks(ArticleCheckQuery query) {
        //  TODO 限制分页查询的单页最大长度
        //  TODO 先行检查站点合理性
        IPage<ArticleContentCheckVO> result = this.getBaseMapper().pageArticleChecks(Page.of(query.getPage(), query.getLimit()), query);

        result.getRecords().forEach(vo -> {
            List<ArticleCheckVO> errorObjs = vo.getErrorObjs();
            if (CollUtil.isEmpty(errorObjs)) {
                return;
            }

            // 一次性分离标题和正文的错误
            Map<Boolean, List<ArticleCheckVO>> errorMap = errorObjs.stream()
                    .collect(Collectors.partitioningBy(e -> e.getArticleLocation() == 1));

            // 处理标题
            String[] markedTitles = markErrorsInContent(
                    vo.getHtmlTitle(),
                    vo.getCleanedTitle(),
                    errorMap.get(true),  // 标题错误
                    true  // 是标题
            );
            vo.setMarkedHtmlTitle(markedTitles[0]);
            vo.setMarkedCleanTitle(markedTitles[1]);

            // 处理正文
            String[] markedContents = markErrorsInContent(
                    vo.getHtmlContent(),
                    vo.getCleanedContent(),
                    errorMap.get(false),  // 正文错误
                    false  // 不是标题
            );
            vo.setMarkedHtmlContent(markedContents[0]);
            vo.setMarkedCleanContent(markedContents[1]);
        });

        return result;
    }


    /**
     * 在内容中标记错误
     *
     * @param htmlContent  HTML内容
     * @param plainContent 纯文本内容
     * @param errors       错误列表
     * @param isTitle      是否是标题
     * @return 标记后的内容数组 [标记后的HTML内容, 标记后的纯文本内容]
     */
    private String[] markErrorsInContent(String htmlContent, String plainContent, List<ArticleCheckVO> errors, boolean isTitle) {
        if (StringUtils.isBlank(htmlContent) || StringUtils.isBlank(plainContent) || CollUtil.isEmpty(errors)) {
            return new String[]{htmlContent, plainContent};
        }

        // 按照位置倒序排序，这样从后向前处理就不会影响前面的位置
        errors.sort((a, b) -> b.getPosition().compareTo(a.getPosition()));

        StringBuilder htmlResult = new StringBuilder(htmlContent);
        StringBuilder plainResult = new StringBuilder(plainContent);
        // 从错误总数开始倒序计数
        int totalErrors = errors.size();

        for (ArticleCheckVO error : errors) {
            Element errorWordSpan = new Element("span")
                    .attr("class", StrUtil.format("sensitive_lv{}_word", error.getErrorLevel()))
                    .attr("data-check-id", error.getCheckId().toString());
            Element errorRemarkSpan = new Element("span")
                    .attr("class", StrUtil.format("sensitive_lv{}_remark", error.getErrorLevel()))
                    .attr("data-check-id", error.getCheckId().toString());
            String remarkText = StrUtil.format("({}错误标注第{}处;{};{};{})", isTitle ? "标题" : "正文", totalErrors--, error.getErrorLevelName(),
                    error.getErrorTypeName(), StrUtil.isBlankIfStr(error.getSuggestWord()) ? "" : StrUtil.format("建议改为:{};", error.getSuggestWord()));
            String errorRemarkTag = errorRemarkSpan.text(remarkText).outerHtml();
            // 处理HTML内容
            String htmlErrorWord = error.getHtmlErrorWord();
            Integer htmlPos = error.getHtmlPosition();
            // 找到错误词在HTML中的实际结束位置
            int htmlEndPos = htmlPos + htmlErrorWord.length();
            htmlResult.replace(htmlPos, htmlEndPos, errorWordSpan.html(htmlErrorWord).outerHtml() + errorRemarkTag);

            // 处理纯文本内容
            String errorWord = error.getErrorWord();
            Integer plainPos = error.getPosition();
            // 找到错误词在HTML中的实际结束位置
            int plainEndPos = plainPos + errorWord.length();
            plainResult.replace(plainPos, plainEndPos, errorWordSpan.text(errorWord).outerHtml() + errorRemarkTag);
        }

        return new String[]{htmlResult.toString(), plainResult.toString()};
    }

    /**
     * 在HTML内容中找到错误词的实际结束位置
     * 考虑到HTML标签可能插入在错误词中间 TODO 可能命中标签属性的文本 但不太好处理
     *
     * @param htmlChars HTML内容的字符数组
     * @param startPos  开始位置
     * @param errorWord 错误词
     * @return 实际的结束位置
     */
    private int findHtmlEndPosition(char[] htmlChars, int startPos, String errorWord) {
        int currentPos = startPos;
        int matchedChars = 0;
        boolean inTag = false;

        while (currentPos < htmlChars.length && matchedChars < errorWord.length()) {
            char c = htmlChars[currentPos];

            if (c == '<') {
                inTag = true;
            } else if (c == '>') {
                inTag = false;
            } else if (!inTag && c == errorWord.charAt(matchedChars)) {
                matchedChars++;
            }

            currentPos++;
        }

        // 如果没有完全匹配，返回开始位置
        if (matchedChars < errorWord.length()) {
            return startPos;
        }

        return currentPos;
    }


    @Override
    public void updateCheckContentStatus(Long contentId, ContentCheckResultDto checkResult) {
        ArticleCheckContent checkContent = new ArticleCheckContent().setId(contentId);
        // 1. 设置检查时间
        checkContent.setCheckTime(new Date());

        // 2. 根据校对结果更新状态
        if (!checkResult.isSuccess()) {
            // 校对失败
            checkContent.setCheckStatus(2); // 检查失败待重试
            checkContent.setFailType(checkResult.getErrorType());
            // 组合失败原因信息
            String failReason = StrUtil.format("失败原因：{}，响应时间：{}ms，详细信息：{}，",
                    checkResult.getErrorMessage(),
                    ObjectUtil.defaultIfNull(checkResult.getResponseTime(), 0),
                    checkResult.getErrorDetail()
            );
            checkContent.setFailReason(failReason);
        } else {
            // 校对成功
            checkContent.setCheckStatus(1); // 已检查
            checkContent.setFailType(null);
            checkContent.setFailReason(null);
        }

        // 3. 保存更新
        this.updateById(checkContent);
    }

} 