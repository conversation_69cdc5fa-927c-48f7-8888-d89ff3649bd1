package cn.dahe.service.impl;


import cn.dahe.dao.AttachmentDao;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.Attachment;
import cn.dahe.query.AttachmentQuery;
import cn.dahe.service.AttachmentService;
import cn.dahe.utils.ListUtils;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class AttachmentServiceImpl extends ServiceImpl<AttachmentDao, Attachment> implements AttachmentService {

    @Override
    public PageResult<Attachment> page(AttachmentQuery query) {
        IPage<Attachment> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage.getTotal());
    }

    private QueryWrapper<Attachment> getWrapper(AttachmentQuery query) {
        QueryWrapper<Attachment> wrapper = Wrappers.query();
        wrapper.eq("is_del", 0);
        if (StringUtils.isNotBlank(query.getKeyword())) {
            wrapper.like("name", query.getKeyword().trim());
        }
//        wrapper.orderByDesc("domain_apply_time");
        return wrapper;
    }

    @Override
    public Result<String> save(Attachment attachment, LoginUserVO user) {
        attachment.setCreateTime(new Date());
        baseMapper.insert(attachment);
        return Result.ok();
    }

    @Override
    public Result<String> update(String id, Attachment vo) {
        Attachment attachment = baseMapper.selectById(id);
        if (attachment == null) {
            return Result.error("请选择正确的数据");
        }
        boolean update = updateById(attachment);
        if (update) {
            return Result.ok();
        }
        return Result.error();
    }

    @Override
    public List<Attachment> getByIds(String ids) {
        QueryWrapper<Attachment> queryWrapper = new QueryWrapper<>();
        List<String> list = ListUtils.transferIdsToList(ids);
        if (list.isEmpty()) {
            return new ArrayList<>();
        }
        queryWrapper.in("id", list);
        return  list(queryWrapper);
    }



}