package cn.dahe.service.impl;

import cn.dahe.dao.ChkUpdateSiteColumnDao;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.ChkUpdateSiteColumn;
import cn.dahe.query.ChkUpdateSiteColumnQuery;
import cn.dahe.service.ChkUpdateSiteColumnService;
import cn.dahe.vo.ChkUpdateSiteColumnVO;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 栏目更新检查Service实现类 - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Service
public class ChkUpdateSiteColumnServiceImpl extends BaseServiceImpl<ChkUpdateSiteColumnDao, ChkUpdateSiteColumn> implements ChkUpdateSiteColumnService {

    // ==================== 栏目更新检查概览 ====================

    @Override
    public Map<String, Object> getOverviewStatistics(ChkUpdateSiteColumnQuery query) {
        try {
            Map<String, Object> result = baseMapper.getOverviewStatistics(query);
            return result;
        } catch (Exception e) {
            log.error("获取栏目更新检查概览统计失败，返回兜底数据", e);
        }
        return new HashMap<>();
    }

    // ==================== 栏目更新检查记录 ====================

    @Override
    public PageResult<ChkUpdateSiteColumnVO> page(ChkUpdateSiteColumnQuery query) {
        try {
            Page<ChkUpdateSiteColumnVO> page = new Page<>(query.getPage(), query.getLimit());
            IPage<ChkUpdateSiteColumnVO> result = baseMapper.selectPageWithExtInfo(page, query);
            return PageResult.page(result);
        } catch (Exception e) {
            log.error("查询栏目更新检查记录失败，返回兜底数据", e);
        }
        return  new PageResult<>();
    }

    @Override
    public ChkUpdateSiteColumnVO get(Long id) {
        try {
            ChkUpdateSiteColumnVO result = baseMapper.selectDetailById(id);
            return result;
        } catch (Exception e) {
            log.error("获取栏目更新检查详情失败，返回兜底数据", e);
        }
        return new ChkUpdateSiteColumnVO();
    }

    // ==================== 数据导出 ====================

    @Override
    public void export(ChkUpdateSiteColumnQuery query, LoginUserVO user, HttpServletResponse response) throws IOException {
        try {
            log.info("用户{}导出栏目更新检查记录", user.getUserId());

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("栏目更新检查记录_" + System.currentTimeMillis(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 获取数据
            query.setPage(1);
            query.setLimit(10000); // 导出最多10000条
            PageResult<ChkUpdateSiteColumnVO> pageResult = this.page(query);
            List<ChkUpdateSiteColumnVO> dataList = pageResult.getList();

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("栏目更新检查记录");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"序号", "栏目名称", "栏目分类", "更新时间", "检测状态", "连续不更新天数", "检测结果"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // 填充数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                ChkUpdateSiteColumnVO data = dataList.get(i);

                row.createCell(0).setCellValue(i + 1);
                row.createCell(1).setCellValue(data.getColumnName() != null ? data.getColumnName() : "");
                row.createCell(2).setCellValue(data.getColumnCategory() != null ? data.getColumnCategory() : "");
                row.createCell(3).setCellValue(data.getUpdateTime() != null ?
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(data.getUpdateTime()) : "");
                row.createCell(4).setCellValue(data.getCheckStatus() != null ? data.getCheckStatus() : "");
                row.createCell(5).setCellValue(data.getContinuousNotUpdateDays() != null ? data.getContinuousNotUpdateDays() : 0);
                row.createCell(6).setCellValue(data.getCheckResult() != null ? data.getCheckResult() : "");
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("导出栏目更新检查记录失败", e);
            throw new IOException("导出失败：" + e.getMessage());
        }
    }
}
