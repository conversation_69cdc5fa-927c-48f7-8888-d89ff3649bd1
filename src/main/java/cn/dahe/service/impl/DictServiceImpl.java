package cn.dahe.service.impl;


import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.common.constants.StatusConstants;
import cn.dahe.dao.DictDao;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.dto.TreeNode;
import cn.dahe.entity.Department;
import cn.dahe.entity.Dict;
import cn.dahe.query.DictQuery;
import cn.dahe.service.DepartmentService;
import cn.dahe.service.DictService;
import cn.dahe.service.UserService;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.StringUtils;
import cn.dahe.vo.LoginUserVO;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sun.org.apache.bcel.internal.generic.IF_ACMPEQ;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service
@AllArgsConstructor
public class DictServiceImpl extends BaseServiceImpl<DictDao, Dict> implements DictService {

    private UserService userService;
    private DepartmentService departmentService;

    @Override
    public PageResult<Dict> page(DictQuery query) {
        Page dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }


    @Override
    public Result listMediaTreelByProject(String dictIdProject) {
        if (StringUtils.isBlank(dictIdProject)) {
            return Result.ok();
        }
//        新闻摄影  18  新闻漫画  19
//        对应  报纸或者新媒体
//        新闻业务研究  25
//        对应  期刊或报纸
//        其他  对应 报纸
        List<TreeNode> tree = getTree(Integer.parseInt("42"), StatusConstants.COMMON_NORMAL_STRING);
        if (dictIdProject.equals("18") || dictIdProject.equals("19")) {
            tree = tree.stream().filter(treeNode -> !treeNode.getId().equals("45")).collect(Collectors.toList());
        } else if (dictIdProject.equals("25")) {
            tree = tree.stream().filter(treeNode -> !treeNode.getId().equals("44")).collect(Collectors.toList());
        } else {
            tree = tree.stream().filter(treeNode -> treeNode.getId().equals("43")).collect(Collectors.toList());
        }

        return Result.ok(tree);
    }

    @Override
    public Result listMediaTreelByProjectSpecial(String dictIdGenre) {
        if (StringUtils.isBlank(dictIdGenre)) {
            return Result.ok();
        }
        List<TreeNode> tree = getTree(Integer.parseInt("42"), StatusConstants.COMMON_NORMAL_STRING);
        if (dictIdGenre.equals("53") || dictIdGenre.equals("54")) {
            tree = tree.stream().filter(treeNode -> !treeNode.getId().equals("45")).collect(Collectors.toList());
        } else if (dictIdGenre.equals("60")) {
            tree = tree.stream().filter(treeNode -> !treeNode.getId().equals("44")).collect(Collectors.toList());
        } else {
            tree = tree.stream().filter(treeNode -> treeNode.getId().equals("43")).collect(Collectors.toList());
        }

        return Result.ok(tree);
    }


    @Override
    public Result listDictTreelById(String id) {
        return Result.ok(getTree(Integer.parseInt(id), StatusConstants.COMMON_NORMAL_STRING));
    }

    @Override
    public List<TreeNode> listDictTreelProject(LoginUserVO loginUserVO) {
        List<TreeNode> treeNodes;
        List<Dict> dictList = list();

        // 根据状态过滤字典列表
        dictList = dictList.stream()
                .filter(dict -> dict.getStatus() == StatusConstants.COMMON_NORMAL)
                .sorted(Comparator.comparingInt(Dict::getSeq).reversed()) // 排序，SEQ越大越靠前
                .collect(Collectors.toList());


        if (userService.isAdmin(loginUserVO.getRoles())) {
            // 如果是管理员，获取完整的字典项目树
            treeNodes = getUserDictProjectTree(1, dictList, dictList.stream()
                    .map(dict -> String.valueOf(dict.getId()))
                    .collect(Collectors.toList()));
        } else {
            // 如果不是管理员，根据用户所在部门获取对应的字典项目树
            Department department = departmentService.getById(loginUserVO.getDepId());
            treeNodes = getUserDictProjectTree(1, dictList, ListUtils.transferIdsToList(department.getDictProjects()));
        }
        return treeNodes;
    }

    @Override
    public List<TreeNode> listDictTreelProjectByType(String type) {
        List<Dict> dictList = list();
        List<Dict> newDictList = new ArrayList<>();
        // 如果不是管理员，根据用户所在部门获取对应的字典项目树
        Department department = departmentService.getById(SecurityUtils.getLoginUser().getDepId());
        // type 1基础类  2专门类
        // 根据状态过滤字典列表  基础类1  专门类3
        newDictList = dictList.stream()
                .filter(dict -> dict.getStatus() == StatusConstants.COMMON_NORMAL)
                .filter(dict -> type.equals("1") ? dict.getPid().equals(2) : dict.getPid().equals(3))
                .sorted(Comparator.comparingInt(Dict::getSeq).reversed())
                .collect(Collectors.toList());
        if (type.equals("1")) {
            newDictList.add(this.getById(2));
        } else {
            newDictList.add(this.getById(3));
        }

        List<String> userDictProjectList = ListUtils.transferIdsToList(department.getDictProjects());
        for (Dict dict : newDictList) {
            boolean optional = dict.getId().equals(2) || dict.getId().equals(3) ||
                    (dict.getOptional() == StatusConstants.COMMON_NORMAL && userDictProjectList.contains(String.valueOf(dict.getId())));
            dict.setOptional(optional ? 1 : 0);
        }
        return convertToTree(newDictList);
    }


    //根据id查找树
    public List<TreeNode> getTree(Integer id, String status) {
        List<Dict> dictList = list();
        // 根据状态过滤字典列表
        if (StringUtils.isNotBlank(status) && !StatusConstants.QUERY_STRING_ALL.equals(status)) {
            dictList = dictList.stream()
                    .filter(dict -> String.valueOf(dict.getStatus()).equals(status))
                    .sorted(Comparator.comparingInt(Dict::getSeq).reversed()) // 排序，SEQ越大越靠前
                    .collect(Collectors.toList());
        }
        return findChildren(id, dictList);
    }

    //根据用户部门的可参选项目去渲染树
    public static List<TreeNode> getUserDictProjectTree(int id, List<Dict> dictList, List<String> userDictProjectList) {
        List<TreeNode> treeNodes = new ArrayList<>();
        for (Dict dict : dictList) {
            if (dict.getPid() == id) {
                TreeNode treeNode = new TreeNode();
                Integer dictId = dict.getId();
                treeNode.setId(String.valueOf(dictId));
                treeNode.setName(dict.getName());
                treeNode.setChecked(dict.getStatus() == StatusConstants.COMMON_NORMAL);
                treeNode.setPid(String.valueOf(dict.getPid()));
                // 判断节点是否可选   //特殊最外面两层永远可选
                boolean optional = dictId.equals(2) || dictId.equals(3) ||
                        (dict.getOptional() == StatusConstants.COMMON_NORMAL && userDictProjectList.contains(String.valueOf(dictId)));
                treeNode.setDisabled(!optional);
                treeNode.setChildrenNode(getUserDictProjectTree(dictId, dictList, userDictProjectList)); // 传递当前节点的 ID
                treeNodes.add(treeNode);
            }
        }
        return treeNodes;
    }

    /**
     * 查找指定父节点下的所有子节点并构建树形结构
     *
     * @param id       父节点的ID
     * @param dictList 包含所有节点信息的字典列表
     * @return 返回构建好的树形结构节点列表
     */
    private static List<TreeNode> findChildren(int id, List<Dict> dictList) {
        List<TreeNode> treeNodes = new ArrayList<>();
        for (Dict dict : dictList) {
            // 如果当前字典项的父节点ID等于给定的ID，则表示这是给定ID节点的子节点
            if (dict.getPid() == id) {
                TreeNode treeNode = new TreeNode();
                // 设置子节点的属性值
                treeNode.setId(String.valueOf(dict.getId()));
                treeNode.setName(dict.getName());
                treeNode.setDisabled(dict.getOptional() == StatusConstants.COMMON_DISABLE);
                treeNode.setChecked(dict.getStatus() == StatusConstants.COMMON_NORMAL);
                treeNode.setPid(String.valueOf(dict.getPid()));
                // 递归查找当前节点的子节点，并设置为当前节点的子节点列表
                treeNode.setChildrenNode(findChildren(dict.getId(), dictList));
                // 将当前节点添加到节点列表中
                treeNodes.add(treeNode);
            }
        }
        // 返回构建好的节点列表
        return treeNodes;
    }


    //传入一个list集合来生成树
    public static List<TreeNode> convertToTree(List<Dict> dictList) {
        Map<Integer, TreeNode> map = new HashMap<>();
        for (Dict dict : dictList) {
            TreeNode treeNode = new TreeNode();
            treeNode.setId(String.valueOf(dict.getId()));
            treeNode.setName(dict.getName());
            treeNode.setDisabled(dict.getOptional() == StatusConstants.COMMON_DISABLE);
            treeNode.setChecked(dict.getStatus() == StatusConstants.COMMON_NORMAL);
            treeNode.setPid(String.valueOf(dict.getPid()));
            map.put(dict.getId(), treeNode);
        }

        List<TreeNode> treeNodes = new ArrayList<>();
        for (Dict dict : dictList) {
            Integer pid = dict.getPid();
            TreeNode node = map.get(dict.getId());
            if (pid != null && map.containsKey(pid)) {
                TreeNode parent = map.get(pid);
                parent.getChildrenNode().add(node);
            } else {
                treeNodes.add(node);
            }
        }
        return treeNodes;
    }


    private QueryWrapper<Dict> getWrapper(DictQuery query) {
        QueryWrapper<Dict> wrapper = Wrappers.query();
        if (StringUtils.isNotBlank(query.getKeyword())) {
            wrapper.like("name", query.getKeyword());
        }
        if (StringUtils.isNotBlank(query.getPid())) {
            wrapper.eq("pid", query.getPid());
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            wrapper.eq("status", query.getStatus());
        }
        //seq越大越靠后
        wrapper.orderByAsc("seq");
        return wrapper;
    }


    @Override
    public Result<String> save(Dict dict, LoginUserVO user) {
        String errorMsg = checkDict(dict);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(errorMsg);
        }
        Dict entity = new Dict();
        entity.setName(dict.getName());
        entity.setType(getTypeByPid(dict.getPid()));
        entity.setNote(StringUtils.defaultIfBlank(dict.getNote(), ""));
        entity.setSeq(dict.getSeq() == null ? 0 : dict.getSeq());
        entity.setPid(dict.getPid() == null ? 0 : dict.getPid());
        entity.setStatus(StatusConstants.COMMON_NORMAL);
        if (!this.save(entity)) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> update(String id, Dict vo, LoginUserVO user) {
        Dict dict = baseMapper.selectById(id);
        if (dict == null) {
            return Result.error("请选择正确的数据");
        }
        vo.setId(dict.getId());
        String errorMsg = checkDict(dict);
        if (StringUtils.isNotBlank(errorMsg)) {
            return Result.error(errorMsg);
        }
        dict.setName(vo.getName());
        dict.setType(getTypeByPid(vo.getPid()));
        dict.setNote(StringUtils.defaultIfBlank(vo.getNote(), ""));
        dict.setSeq(vo.getSeq() == null ? 0 : vo.getSeq());
        dict.setPid(vo.getPid() == null ? 0 : vo.getPid());
        if (!updateById(dict)) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateStatus(String id, int status) {
        Dict dict = baseMapper.selectById(id);
        if (dict == null) {
            return Result.error("请选择正确的数据");
        }
        dict.setStatus(status);
        if (!updateById(dict)) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public List<String> getByType(String type) {
        QueryWrapper<Dict> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", type);
        return list(queryWrapper).stream().map(dict -> String.valueOf(dict.getId())).collect(Collectors.toList());
    }

    //根据pid确定type值（同一大类得type值都相同）
    private String getTypeByPid(Integer pid) {
        Dict byId = getById(pid);
        if (byId == null) {
            return IdUtil.fastSimpleUUID();
        } else {
            return byId.getType();
        }
    }


    private String checkDict(Dict dict) {
        if (StringUtils.isBlank(dict.getName())) {
            return "请填写字典名称";
        }
//        List<Dict> byName = getByName(dict.getName(), dict.getId());
//        if (!byName.isEmpty()) {
//            return "名称重复";
//        }
        return "";
    }


    //根据名称查找
    public List<Dict> getByName(String name, Integer excludeId) {
        QueryWrapper<Dict> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return list(queryWrapper);
    }

    public static void main(String[] args) {
        ArrayList<String> list = new ArrayList<>();
        list.add("7");
        list.add("25");
        System.out.println();
    }

}

