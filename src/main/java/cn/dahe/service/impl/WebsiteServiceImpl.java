package cn.dahe.service.impl;

import cn.dahe.common.constants.StatusConstants;
import cn.dahe.dao.WebSiteDao;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.Website;
import cn.dahe.query.WebsiteQuery;
import cn.dahe.service.WebsiteService;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.StringUtils;
import cn.dahe.vo.LoginUserVO;
import cn.dahe.vo.WebsiteVO;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class WebsiteServiceImpl extends BaseServiceImpl<WebSiteDao, Website> implements WebsiteService {


    @Override
    public PageResult<Website> page(WebsiteQuery query) {
        IPage<Website> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public PageResult<Website> pageByWebIds(String webIds, int page, int limit) {
        WebsiteQuery query = new WebsiteQuery();
        query.setWebId(webIds);
        query.setPage(page);
        query.setLimit(limit);
        IPage<Website> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public PageResult<Website> pageByWebIds(List<Long> websiteIds, int page, int limit) {
        Page<Website> pageResult = this.query().in(CollUtil.isNotEmpty(websiteIds), "id", websiteIds).page(Page.of(page, limit));
        return PageResult.page(pageResult);
    }


    @Override
    public Result<String> save(Website vo, LoginUserVO user) {
        if (StringUtils.isBlank(vo.getWebUrl())) {
            return Result.error("网站URL不能为空");
        }
        if (StringUtils.isBlank(vo.getWebName())) {
            return Result.error("网站名称不能为空");
        }
        Website entity = new Website();
        entity.setWebUrl(vo.getWebUrl());
        entity.setWebName(vo.getWebName());
        entity.setWebCodeId(vo.getWebCodeId());
        entity.setCreateTime(new Date());
        entity.setCreateUserId(user.getUserId());
        entity.setCreateUserName(user.getUsername());
        entity.setStatus(StatusConstants.COMMON_NORMAL);
        boolean save = this.save(entity);
        if (!save) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> update(Website vo, LoginUserVO user) {
        Website oldWebsite = this.getById(vo.getId());
        if (oldWebsite == null) {
            return Result.error("Id传递错误");
        }
        if (StringUtils.isBlank(vo.getWebUrl())) {
            return Result.error("网站URL不能为空");
        }
        if (StringUtils.isBlank(vo.getWebName())) {
            return Result.error("网站名称不能为空");
        }
        oldWebsite.setWebUrl(vo.getWebUrl());
        oldWebsite.setWebName(vo.getWebName());
        oldWebsite.setWebCodeId(vo.getWebCodeId());
        boolean saveOrUpdate = this.saveOrUpdate(oldWebsite);
        if (!saveOrUpdate) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateStatus(String id, LoginUserVO user) {
        //禁用启用
        Website oldWebsite = this.getById(id);
        if (oldWebsite == null) {
            return Result.error("Id传递错误");
        }
        oldWebsite.setStatus(oldWebsite.getStatus() == StatusConstants.COMMON_NORMAL ? StatusConstants.COMMON_DISABLE : StatusConstants.COMMON_NORMAL);
        boolean update = updateById(oldWebsite);
        if (!update) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public List<Website> listByStatus(int status) {
        QueryWrapper<Website> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);
        return list(queryWrapper);
    }

    @Override
    public List<WebsiteVO> listTotal() {
        return this.getBaseMapper().listTotal();
    }

    private QueryWrapper<Website> getWrapper(WebsiteQuery query) {
        QueryWrapper<Website> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(query.getName())) {
            queryWrapper.like("web_name", query.getName());
        }
        if (StringUtils.isNotBlank(query.getUrl())) {
            queryWrapper.like("web_url", query.getUrl());
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq("status", query.getStatus());
        }
        if (StringUtils.isNotBlank(query.getWebId())) {
            queryWrapper.in("id", ListUtils.transferIdsToList(query.getWebId()));
        }
        return queryWrapper;
    }
}
