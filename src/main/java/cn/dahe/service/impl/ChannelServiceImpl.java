package cn.dahe.service.impl;

import cn.dahe.common.constants.StatusConstants;
import cn.dahe.dao.ChannelDao;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.Channel;
import cn.dahe.entity.Website;
import cn.dahe.query.ChannelQuery;
import cn.dahe.service.ChannelService;
import cn.dahe.service.WebsiteService;
import cn.dahe.utils.StringUtils;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 *
 */
@Service
public class ChannelServiceImpl extends BaseServiceImpl<ChannelDao, Channel> implements ChannelService {

    @Resource
    private WebsiteService webSiteService;

    @Override
    public PageResult<Channel> page(ChannelQuery query) {
        IPage<Channel> dataPage = baseMapper.selectPage(new Page(query.getPage(), query.getLimit()), getWrapper(query));
        return new PageResult<>(dataPage.getRecords(), dataPage);
    }

    @Override
    public Result<String> save(Channel vo, LoginUserVO user) {
        if (StringUtils.isBlank(vo.getUrl())) {
            return Result.error("栏目链接不能为空");
        }
        Website webSite = webSiteService.getById(vo.getSiteId());
        if (webSite == null) {
            return Result.error("网站选择错误");
        }
        vo.setSiteName(webSite.getWebName());
        vo.setCreateTime(new Date());
        vo.setCreateUserId(user.getUserId());
        vo.setCreateUserName(user.getUsername());
        vo.setLastModifyTime(vo.getCreateTime());
        boolean save = this.save(vo);
        if (!save) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> update(Channel vo, LoginUserVO user) {
        Channel oldChannel = this.getById(vo.getId());
        if (oldChannel == null) {
            return Result.error("栏目ID选择错误");
        }
        Website webSite = webSiteService.getById(vo.getSiteId());
        if (webSite == null) {
            return Result.error("网站选择错误");
        }
        vo.setSiteName(webSite.getWebName());
        vo.setCreateTime(oldChannel.getCreateTime());
        vo.setLastModifyTime(new Date());
        boolean saveOrUpdate = this.saveOrUpdate(vo);
        if (!saveOrUpdate) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateStatus(String id, LoginUserVO user) {
        //更改抓取状态
        Channel oldChannel = this.getById(id);
        if (oldChannel == null) {
            return Result.error("Id传递错误");
        }
        oldChannel.setEnable(oldChannel.getEnable() == StatusConstants.COMMON_NORMAL ? StatusConstants.COMMON_DISABLE : StatusConstants.COMMON_NORMAL);
        boolean update = updateById(oldChannel);
        if (!update) {
            return Result.error();
        }
        return Result.ok();
    }


    private QueryWrapper<Channel> getWrapper(ChannelQuery query) {
        QueryWrapper<Channel> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(query.getName())) {
            queryWrapper.like("name", query.getName());
        }
        if (StringUtils.isNotBlank(query.getUrl())) {
            queryWrapper.like("url", query.getUrl());
        }
        if (StringUtils.isNotBlank(query.getEnable())) {
            queryWrapper.eq("enable", query.getEnable());
        }
        if (StringUtils.isNotBlank(query.getWebSiteId())) {
            queryWrapper.eq("site_id", query.getWebSiteId());
        }
        return queryWrapper;
    }
}
