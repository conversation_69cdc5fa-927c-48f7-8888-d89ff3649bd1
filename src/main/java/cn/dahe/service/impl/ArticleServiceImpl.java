package cn.dahe.service.impl;

import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.dao.ArticleDao;
import cn.dahe.dto.ArticleDto;
import cn.dahe.dto.ArticleSaveDto;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.Article;
import cn.dahe.enums.ArticleDisposalStatusEnum;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.query.ArticleQuery;
import cn.dahe.service.ArticleService;
import cn.dahe.utils.ListUtils;
import cn.dahe.utils.StringUtils;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 采集文章Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class ArticleServiceImpl extends BaseServiceImpl<ArticleDao, Article> implements ArticleService {
    @Override
    public PageResult<ArticleDto> page(ArticleQuery query) {
        PageInfo<ArticleDto> objectPageInfo = PageHelper.startPage(query.getPage(), query.getLimit())
                .doSelectPageInfo(() ->
                        baseMapper.listByFilters(
                                query.getGroupType(),
                                query.getBeginTime(),
                                query.getEndTime(),
                                query.getErrorWord(),
                                query.getSuggestWord(),
                                query.getKeyword(),       // cleanTitle
                                query.getKeyword(),       // cleanContent
                                query.getSource(),
                                ListUtils.transferIdsToList(query.getWebId()),
                                ListUtils.transferIdsToList(query.getAuditStatus()),
                                ListUtils.transferIdsToList(query.getDisposalStatus()),
                                ListUtils.transferIdsToList(query.getRectifyStatus()),
                                ListUtils.transferIdsToList(query.getErrorLevel()),
                                ListUtils.transferIdsToList(query.getErrorType()),
                                ListUtils.transferIdsToList(query.getErrorAuditStatus())
                        )
                );
        return new PageResult<>(objectPageInfo.getList(), objectPageInfo);
    }

    @Override
    public Result<String> updateAuditStatusReject(String articleIds, LoginUserVO loginUserVO) {
        if (StringUtils.isBlank(articleIds)) {
            return Result.error("文章ID不能为空");
        }
        boolean b = updateAuditStatusByIdsAndStatus(articleIds, AuditStatusEnum.REJECT.getType());
        if (!b) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateAuditStatusPass(String articleIds, LoginUserVO loginUserVO) {
        if (StringUtils.isBlank(articleIds)) {
            return Result.error("文章ID不能为空");
        }
        boolean b = updateAuditStatusByIdsAndStatus(articleIds, AuditStatusEnum.PASS.getType());
        if (!b) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateAuditStatusWithdraw(String articleIds, LoginUserVO loginUserVO) {
        if (StringUtils.isBlank(articleIds)) {
            return Result.error("文章ID不能为空");
        }
        boolean b = updateAuditStatusByIdsAndStatus(articleIds, AuditStatusEnum.WAITING_FOR_REVIEW.getType());
        if (!b) {
            return Result.error();
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateDisposalStatus(String articleIds,
                                               int disposalStatus,
                                               String remark,
                                               LoginUserVO loginUserVO) {
        if (StringUtils.isBlank(articleIds)) {
            return Result.error("文章ID不能为空");
        }
        ArticleDisposalStatusEnum enumByType = ArticleDisposalStatusEnum.getEnumByType(disposalStatus);
        if (enumByType == null) {
            return Result.error("处置状态选择错误");
        }
        boolean b = updateupdateDisposalStatusByIdsAndStatus(articleIds, enumByType.getType(), remark);
        if (!b) {
            return Result.error();
        }
        return Result.ok();
    }

    private boolean updateupdateDisposalStatusByIdsAndStatus(String articleIds, int status, String remark) {
        UpdateWrapper<Article> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("disposal_status", status);
        updateWrapper.set("disposal_remark", remark);
        updateWrapper.in("id", ListUtils.transferIdsToList(articleIds));
        return this.update(updateWrapper);
    }

    private boolean updateAuditStatusByIdsAndStatus(String articleIds, int status) {
        UpdateWrapper<Article> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("audit_status", status);
        updateWrapper.in("id", ListUtils.transferIdsToList(articleIds));
        return this.update(updateWrapper);
    }


    @Override
    public Boolean updateArticleAuditStatus(List<Long> articleIds, AuditStatusEnum auditStatus) {
        //  TODO 日志
        LoginUserVO loginUser = SecurityUtils.getLoginUser();
        LambdaUpdateWrapper<Article> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Article::getAuditStatus, auditStatus.getType());
        if (auditStatus != AuditStatusEnum.WAITING_FOR_REVIEW) {
            updateWrapper.set(Article::getAuditUserId, loginUser.getUserId());
            updateWrapper.set(Article::getAuditTime, new Date());
        }
        updateWrapper.in(Article::getId, articleIds);
        return this.update(updateWrapper);
    }

    @Override
    public Article savePushArticle(ArticleSaveDto saveDTO) {
        Article article = new Article();
        article.setWebsiteId(saveDTO.getWebsiteId());
        article.setLinkUrl(saveDTO.getUrl());
        article.setTitle(saveDTO.getTitle());

        article.setCreateTime(new Date());
        article.setAcquisitionTime(new Date());
        article.setPubTime(saveDTO.getPubTime());
        article.setWriteTime(saveDTO.getWriteTime());
        // 设置可选字段
        article.setSource(saveDTO.getSource());
        article.setEditor(saveDTO.getAuthor());
        article.setColumnId(saveDTO.getChannelId());

        this.save(article);
        return article;
    }

    @Override
    public void updateCheckInfo(Long articleId, Long articleContentId) {
        this.lambdaUpdate()
                .eq(Article::getId, articleId)
                .set(Article::getCheckContentId, articleContentId)
                .set(Article::getCheckTime, new Date())
                .update();
    }


}