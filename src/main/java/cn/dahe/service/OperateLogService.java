package cn.dahe.service;


import cn.dahe.dto.PageResult;
import cn.dahe.entity.OperateLog;
import cn.dahe.query.OperateLogQuery;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface OperateLogService extends IService<OperateLog> {


    PageResult<OperateLog> page(OperateLogQuery query);


    Long countByRequestUrlAndTime(String beginTime,String endTime,String url);

}