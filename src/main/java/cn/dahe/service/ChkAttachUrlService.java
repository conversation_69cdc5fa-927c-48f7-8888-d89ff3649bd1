package cn.dahe.service;

import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.ChkAttachUrl;
import cn.dahe.query.ChkAttachUrlQuery;
import cn.dahe.vo.ChkAttachUrlVO;
import cn.dahe.vo.LoginUserVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 附件检查Service - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface ChkAttachUrlService extends BaseService<ChkAttachUrl> {

    // ==================== 附件检查记录 ====================

    /**
     * 分页查询附件检查记录
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageResult<ChkAttachUrlVO> page(ChkAttachUrlQuery query);

    /**
     * 根据ID获取附件检查详情
     *
     * @param id ID
     * @return 详情
     */
    ChkAttachUrlVO get(Long id);

    // ==================== 数据导出 ====================

    /**
     * 导出附件检查记录
     *
     * @param query 查询参数
     * @param user 当前用户
     * @param response HTTP响应
     */
    void export(ChkAttachUrlQuery query, LoginUserVO user, HttpServletResponse response) throws IOException;
}
