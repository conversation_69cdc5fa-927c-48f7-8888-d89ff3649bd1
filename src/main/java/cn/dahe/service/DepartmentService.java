package cn.dahe.service;

import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.Department;
import cn.dahe.query.DepartmentQuery;
import cn.dahe.vo.DepartmentVO;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
 *
 */
public interface DepartmentService extends IService<Department> {


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult page(DepartmentQuery query);

    List<DepartmentVO> getByStatusAndName(String name,Integer status);

    Result<String> save(DepartmentVO vo, LoginUserVO loginUserVO);

    Result<String> update(String id,DepartmentVO vo, LoginUserVO loginUserVO);


}