package cn.dahe.service;

import cn.dahe.query.ArticleCheckStatQuery;
import cn.dahe.query.ArticleCheckWordStatQuery;
import cn.dahe.vo.WebsiteArticleCheckStatsVO;
import cn.dahe.vo.WebsiteArticleCheckTotalStatsVO;
import cn.dahe.vo.WebsiteArticleCheckWordStatsVO;
import cn.dahe.vo.WebsiteArticleCheckWordTotalStatsVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 网站文章错误统计服务接口
 */
public interface ArticleCheckStatsService {

    
    /**
     * 获取网站错误统计信息列表
     */
    IPage<WebsiteArticleCheckStatsVO> pageStatsByWebsite(ArticleCheckStatQuery query);
    
    /**
     * 获取总体统计概况
     */
    WebsiteArticleCheckTotalStatsVO queryTotalStats(ArticleCheckStatQuery query);

    WebsiteArticleCheckWordTotalStatsVO queryWordTotalStats(ArticleCheckWordStatQuery query);

    IPage<WebsiteArticleCheckWordStatsVO> pageWordStatsByWebsite(ArticleCheckWordStatQuery query);
} 