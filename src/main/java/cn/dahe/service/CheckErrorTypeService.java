package cn.dahe.service;

import cn.dahe.entity.CheckErrorType;
import cn.dahe.utils.SpringUtils;
import cn.dahe.vo.check.BaseErrorTypeVO;
import cn.dahe.vo.check.FirstLevelErrorTypeVO;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 错误类型Service
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface CheckErrorTypeService extends BaseService<CheckErrorType> {

    /**
     * 获取错误类型树
     */
    List<FirstLevelErrorTypeVO> getTypeTree();

    /**
     * 获取指定层级的错误类型列表
     */
    List<CheckErrorType> getTypesByLevel(Integer level);

    /**
     * 获取指定父级的子类型列表
     */
    List<CheckErrorType> getChildTypes(Long parentId);

    List<BaseErrorTypeVO> listTypeName();

    /**
     * 刷新错误类型名称缓存
     */
    void refreshErrorTypeNameCache();

    Map<Long, String> TYPE_NAME_CACHE = new ConcurrentHashMap<>();

    /**
     * 根据错误类型ID获取类型名称
     * 二级分类返回其分类名称
     * 三级分类返回"二级分类名-三级分类名"
     *
     * @param typeId 错误类型ID
     * @return String 错误类型名称
     */
    static String getTypeName(Long typeId) {
        if (TYPE_NAME_CACHE.isEmpty()) {
            SpringUtils.getBean(CheckErrorTypeService.class).refreshErrorTypeNameCache();
        }
        return TYPE_NAME_CACHE.get(typeId);
    }


} 