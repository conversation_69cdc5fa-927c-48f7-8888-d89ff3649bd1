package cn.dahe.service;

import cn.dahe.dto.ContentCheckResultDto;
import cn.dahe.entity.ArticleCheckContent;
import cn.dahe.query.ArticleCheckQuery;
import cn.dahe.vo.check.ArticleContentCheckVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 文章检查内容服务接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface ArticleCheckContentService extends IService<ArticleCheckContent> {
    /**
     * 根据文章ID获取检查内容
     *
     * @param articleId 文章ID
     * @return 检查内容
     */
    ArticleCheckContent getByArticleId(Long articleId);

    /**
     * 分页查询文章检查结果
     *
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<ArticleContentCheckVO> pageArticleChecks(ArticleCheckQuery query);
    /**
     * 更新文章校对内容状态
     *
     * @param contentId   文章校对内容
     * @param checkResult 校对结果
     */
    void updateCheckContentStatus(Long contentId, ContentCheckResultDto checkResult);



} 