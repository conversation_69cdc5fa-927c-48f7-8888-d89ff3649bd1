package cn.dahe.service;

import cn.dahe.dto.PageResult;
import cn.dahe.dto.WebsiteAccessOverviewDto;
import cn.dahe.dto.WebsiteOutLinkCheckRecordDto;
import cn.dahe.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.WebsiteOutLinkCheckRecord;
import cn.dahe.query.WebsiteOutLinkCheckRecordQuery;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface WebsiteOutLinkCheckRecordService extends IService<WebsiteOutLinkCheckRecord> {

    /**
     * 分页查询-检查记录详细信息
     *
     * @param query
     * @return
     */
    PageResult<WebsiteOutLinkCheckRecord> pageCheckRecordDetail(WebsiteOutLinkCheckRecordQuery query);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    WebsiteUpdateStatsDto totalOverview(WebsiteOutLinkCheckRecordQuery query);


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<WebsiteAccessOverviewDto> pageWebsiteStats(WebsiteOutLinkCheckRecordQuery query);

    /**
     * 更新过滤状态，按照网站id和外链链接进行更新
     * @param filter
     * @param webId
     * @param linkurl
     * @return
     */
    boolean updateFilterByWebIdAndLinkUrl(int filter,int webId, String linkurl);
}