package cn.dahe.service;

import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.SystemNotice;
import cn.dahe.query.SystemNoticeQuery;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-10
 */
public interface SystemNoticeService extends IService<SystemNotice> {


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<SystemNotice> page(SystemNoticeQuery query);

    List<SystemNotice> listAll(SystemNoticeQuery query);

    /**
     * 添加
     *
     * @param systemNotice
     * @param user
     * @return
     */
    Result<String> save(SystemNotice systemNotice, LoginUserVO user);

    /**
     * 编辑
     *
     * @param systemNotice
     * @param user
     * @return
     */
    Result<String> update(SystemNotice systemNotice, LoginUserVO user);

    /**
     * 禁用or启用
     *
     * @param id
     * @param user
     * @return
     */
    Result<String> updateStatus(String id, LoginUserVO user);


}
