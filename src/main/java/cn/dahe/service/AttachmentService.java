package cn.dahe.service;

import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.Attachment;
import cn.dahe.query.AttachmentQuery;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface AttachmentService extends IService<Attachment> {
    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<Attachment> page(AttachmentQuery query);

    /**
     * 添加
     *
     * @param attachment
     * @param user
     * @return
     */
    Result<String> save(Attachment attachment, LoginUserVO user);

    /**
     * 编辑
     *
     * @param id
     * @param attachment
     * @return
     */
    Result<String> update(String id, Attachment attachment);


    /**
     * 编辑
     *
     * @param ids
     * @return
     */
    List<Attachment> getByIds(String ids);
}