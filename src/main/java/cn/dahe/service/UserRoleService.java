package cn.dahe.service;

import cn.dahe.dto.Result;
import cn.dahe.entity.UserRole;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface UserRoleService extends IService<UserRole> {
    /**
     * 添加
     *
     * @param userRole
     * @param user
     * @return
     */
    Result<String> save(UserRole userRole, LoginUserVO user);

    /**
     * 编辑
     *
     * @param id
     * @param userRole
     * @return
     */
    Result<String> update(String id, UserRole userRole);


    /**
     * 根据用户ID查找角色
     *
     * @param userId
     * @return
     */
    List<UserRole> listByUserId(Integer userId);

    /**
     * 根据角色ID查找用户
     *
     * @param roleId
     * @return
     */
    List<UserRole> listByRoleId(Integer roleId);


    /**
     * 根据用户id删除
     *
     * @param userId
     * @return
     */
    Result removeByUserId(Integer userId);


    /**
     * 通过角色ID和用户ID保存
     *
     * @param userId
     * @param roleIds
     * @return
     */
    Result saveByUserIdAndRoleIds(Integer userId, String roleIds);
}