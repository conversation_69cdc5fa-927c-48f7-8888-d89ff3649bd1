package cn.dahe.service;

import cn.dahe.dto.PageResult;
import cn.dahe.dto.WebsiteAccessOverviewDto;
import cn.dahe.dto.WebsiteDeadLinkCheckRecordDto;
import cn.dahe.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.WebsiteDeadLinkCheckRecord;
import cn.dahe.query.WebsiteAccessRecordQuery;
import cn.dahe.query.WebsiteDeadLinkCheckRecordQuery;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface WebsiteDeadLinkCheckRecordService extends IService<WebsiteDeadLinkCheckRecord> {

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<WebsiteDeadLinkCheckRecord> page(WebsiteDeadLinkCheckRecordQuery query);

    /**
     * 分页查询-页面死链接详细信息
     *
     * @param query
     * @return
     */
    PageResult<WebsiteDeadLinkCheckRecordDto> pageDeadLinkDetail(WebsiteDeadLinkCheckRecordQuery query);

    /**
     * 分页查询-检查记录详细信息
     *
     * @param query
     * @return
     */
    PageResult<WebsiteDeadLinkCheckRecord> pageCheckRecordDetail(WebsiteDeadLinkCheckRecordQuery query);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    WebsiteUpdateStatsDto totalOverview(WebsiteDeadLinkCheckRecordQuery query);


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<WebsiteAccessOverviewDto> pageWebsiteStats(WebsiteDeadLinkCheckRecordQuery query);
}