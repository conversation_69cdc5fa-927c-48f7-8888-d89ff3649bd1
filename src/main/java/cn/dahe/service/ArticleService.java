package cn.dahe.service;

import cn.dahe.dto.ArticleDto;
import cn.dahe.dto.ArticleSaveDto;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.Article;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.query.ArticleQuery;
import cn.dahe.vo.LoginUserVO;

import java.util.List;

/**
 * 采集文章Service
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface ArticleService extends BaseService<Article> {

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<ArticleDto> page(ArticleQuery query);

    /**
     * 审核驳回
     *
     * @param articleIds
     * @param loginUserVO
     * @return
     */
    Result<String> updateAuditStatusReject(String articleIds, LoginUserVO loginUserVO);

    /**
     * 审核通过
     *
     * @param articleIds
     * @param loginUserVO
     * @return
     */
    Result<String> updateAuditStatusPass(String articleIds, LoginUserVO loginUserVO);

    /**
     * 审核撤回
     *
     * @param articleIds
     * @param loginUserVO
     * @return
     */
    Result<String> updateAuditStatusWithdraw(String articleIds, LoginUserVO loginUserVO);

    /**
     * 处置
     * @param articleIds
     * @param disposalStatus
     * @param remark
     * @param loginUserVO
     * @return
     */
    Result<String> updateDisposalStatus(String articleIds, int disposalStatus, String remark, LoginUserVO loginUserVO);

    Boolean updateArticleAuditStatus(List<Long> articleIds, AuditStatusEnum auditStatus);

    /**
     * 保存推送的文章
     *
     * @param saveDTO 文章保存参数
     * @return 保存的文章实体
     */
    Article savePushArticle(ArticleSaveDto saveDTO);

    void updateCheckInfo(Long articleId, Long articleContentId);
} 