package cn.dahe.service;

import cn.dahe.dto.Result;
import cn.dahe.entity.WebsiteGroup;
import cn.dahe.utils.SpringUtils;
import cn.dahe.vo.LoginUserVO;
import cn.dahe.vo.WebsiteGroupVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 网站分组Service
 */
public interface WebsiteGroupService extends IService<WebsiteGroup> {

    /**
     * 获取所有可用的分组列表
     *
     * @return 分组列表
     */
    List<WebsiteGroup> listAll();

    /**
     * 添加分组
     *
     * @param group 分组信息
     * @param user  当前用户
     * @return 操作结果
     */
    Result<String> save(WebsiteGroup group, LoginUserVO user);

    /**
     * 删除分组
     *
     * @param id   分组ID
     * @param user 当前用户
     * @return 操作结果
     */
    Result<String> delete(Integer id, LoginUserVO user);

    /**
     * 刷新分组名称缓存
     */
    void refreshGroupNameCache();

    /**
     * 获取所有分组及其网站列表
     *
     * @return 分组及其网站列表
     */
    List<WebsiteGroupVO> listWithWebsites();

    Map<Integer, String> GROUP_NAME_CACHE = new ConcurrentHashMap<>();

    /**
     * 根据分组ID获取分组名称
     *
     * @param groupId 分组ID
     * @return 分组名称
     */
    static String getGroupName(Integer groupId) {
        if (groupId == null) {
            return "未分组";
        }
        if (GROUP_NAME_CACHE.isEmpty()) {
            SpringUtils.getBean(WebsiteGroupService.class).refreshGroupNameCache();
        }
        return GROUP_NAME_CACHE.get(groupId);
    }
} 