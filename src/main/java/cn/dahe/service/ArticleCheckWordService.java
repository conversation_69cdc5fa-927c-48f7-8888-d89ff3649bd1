package cn.dahe.service;

import cn.dahe.entity.ArticleCheckWord;

/**
 * 文章检查错误词库Service接口
 */
public interface ArticleCheckWordService extends BaseService<ArticleCheckWord>{
    
    /**
     * 获取或创建错误记录
     * @param errorWord 错误词
     * @param suggestWord 建议正确用词
     * @param errorTypeFirstId 一级错误类型ID
     * @param errorTypeSecondId 二级错误类型ID
     * @param errorTypeThirdId 三级错误类型ID
     * @param errorLevelId 错误等级ID
     * @return 错误记录ID
     */
    Long getOrCreateCheckWord(String errorWord, String suggestWord,
                              Long errorTypeFirstId, Long errorTypeSecondId,
                              Long errorTypeThirdId, Long errorLevelId);

    /**
     * 根据ID获取错误记录
     */
    ArticleCheckWord getById(Long id);

    /**
     * 更新过滤状态
     */
    boolean updateFilterStatus(Long id, Boolean isFiltered);
} 