package cn.dahe.service;

import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.Role;
import cn.dahe.query.RoleQuery;
import cn.dahe.vo.LoginUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 *
 */
public interface RoleService extends IService<Role> {
    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageResult<Role> page(RoleQuery query);

    /**
     * 根据用户ID查找角色
     *
     * @param userId
     * @return
     */
    List<Role> listByUserId(Integer userId);


    /**
     * 根据用户ID查找角色的SN
     *
     * @param userId
     * @return
     */
    Set<String> listSnByUserId(Integer userId);

    /**
     * 添加
     *
     * @param role
     * @param user
     * @return
     */
    Result<String> save(Role role, String permissionIds, LoginUserVO user);

    /**
     * 编辑
     *
     * @param id
     * @param role
     * @return
     */
    Result<String> update(String id, String permissionIds, Role role);

    /**
     * 修改状态
     *
     * @param id
     * @return
     */
    Result<String> updateStatus(String id);



    Role getBySn(String sn);


    /**
     * 为用户分配角色
     *
     * @return
     */
    Result<String> assignRole(String userId, String roleIds);


    /**
     * 为用户分配角色
     *
     * @return
     */
    Result<String> removeByUserId(String userId, String roleId);


}