package cn.dahe.dao;

import cn.dahe.entity.ChkUpdateSiteIndex;
import cn.dahe.query.ChkUpdateSiteIndexQuery;
import cn.dahe.vo.ChkUpdateSiteIndexVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 首页更新检查Dao - 专注于首页更新检查功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Mapper
public interface ChkUpdateSiteIndexDao extends BaseMapper<ChkUpdateSiteIndex> {

    // ==================== 首页更新检查概览 ====================
    
    /**
     * 获取首页更新检查概览统计
     *
     * @param query 查询参数
     * @return 统计数据
     */
    Map<String, Object> getOverviewStatistics(@Param("query") ChkUpdateSiteIndexQuery query);

    // ==================== 首页更新检查记录 ====================
    
    /**
     * 分页查询首页更新检查记录（带扩展信息）
     *
     * @param page 分页参数
     * @param groupName 分组名称
     * @param websiteName 网站名称
     * @param websiteIndexUrl 网站首页URL
     * @param parseBeginTime 解析开始时间
     * @param parseEndTime 解析结束时间
     * @param createBeginTime 创建开始时间
     * @param createEndTime 创建结束时间
     * @return 分页结果
     */
    IPage<ChkUpdateSiteIndexVO> selectPageWithExtInfo(
            Page<ChkUpdateSiteIndexVO> page,
            @Param("groupName") String groupName,
            @Param("websiteName") String websiteName,
            @Param("websiteIndexUrl") String websiteIndexUrl,
            @Param("parseBeginTime") String parseBeginTime,
            @Param("parseEndTime") String parseEndTime,
            @Param("createBeginTime") String createBeginTime,
            @Param("createEndTime") String createEndTime
    );

    /**
     * 根据ID获取首页更新检查详情
     *
     * @param id ID
     * @return 详情
     */
    ChkUpdateSiteIndexVO selectDetailById(@Param("id") Long id);

}
