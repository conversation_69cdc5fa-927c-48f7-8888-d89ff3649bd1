package cn.dahe.dao;

import cn.dahe.entity.WebsiteGroup;
import cn.dahe.vo.WebsiteGroupVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 网站分组Dao
 */
@Mapper
public interface WebsiteGroupDao extends BaseMapper<WebsiteGroup> {

    /**
     * 获取所有分组及其网站列表
     *
     * @return 分组及其网站列表
     */
    List<WebsiteGroupVO> listWithWebsites();
} 