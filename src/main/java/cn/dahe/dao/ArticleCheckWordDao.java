package cn.dahe.dao;

import cn.dahe.entity.ArticleCheckWord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 文章检查错误词库Dao接口
 */
@Mapper
public interface ArticleCheckWordDao extends BaseMapper<ArticleCheckWord> {
    
    /**
     * 根据错误词和建议词查询记录
     *
     * @param errorWord 错误词
     * @return 错误词记录
     */
    ArticleCheckWord selectByErrorWord(String errorWord);
} 