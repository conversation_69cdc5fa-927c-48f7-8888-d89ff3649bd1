package cn.dahe.dao;

import cn.dahe.entity.ChkAttachUrl;
import cn.dahe.query.ChkAttachUrlQuery;
import cn.dahe.vo.ChkAttachUrlVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 附件检查Dao - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Mapper
public interface ChkAttachUrlDao extends BaseMapper<ChkAttachUrl> {

    // ==================== 附件检查记录 ====================

    /**
     * 分页查询附件检查记录（包含扩展信息）
     *
     * @param page 分页参数
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<ChkAttachUrlVO> selectPageWithExtInfo(
            Page<ChkAttachUrlVO> page,
            @Param("query") ChkAttachUrlQuery query
    );

    /**
     * 根据ID获取附件检查详情
     *
     * @param id ID
     * @return 详情
     */
    ChkAttachUrlVO selectDetailById(@Param("id") Long id);

}
