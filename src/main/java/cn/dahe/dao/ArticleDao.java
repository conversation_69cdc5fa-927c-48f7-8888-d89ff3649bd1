package cn.dahe.dao;

import cn.dahe.dto.ArticleDto;
import cn.dahe.dto.WebsiteAccessOverviewDto;
import cn.dahe.entity.Article;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采集文章Dao
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Mapper
public interface ArticleDao extends BaseMapper<Article> {


    List<ArticleDto> listByFilters(
            @Param("groupType") String groupType,

            @Param("beginTime") String beginTime,
            @Param("endTime") String endTime,

            @Param("errorWord") String errorWord,
            @Param("suggestWord") String suggestWord,
            @Param("cleanTitle") String cleanTitle,
            @Param("cleanContent") String cleanContent,
            @Param("source") String source,

            @Param("webIdList") List<String> webIdList,
            @Param("auditStatusList") List<String> auditStatusList,
            @Param("disposalStatusList") List<String> disposalStatusList,
            @Param("rectifyStatusList") List<String> rectifyStatusList,
            @Param("errorLevelList") List<String> errorLevelList,
            @Param("errorTypeList") List<String> errorTypeList,
            @Param("errorAuditStatusList") List<String> errorAuditStatusList
    );


}