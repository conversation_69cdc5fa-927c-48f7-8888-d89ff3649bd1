package cn.dahe.dao;

import cn.dahe.entity.ArticleCheckContent;
import cn.dahe.query.ArticleCheckQuery;
import cn.dahe.vo.check.ArticleCheckVO;
import cn.dahe.vo.check.ArticleContentCheckVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文章检查内容数据访问接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Mapper
public interface ArticleCheckContentDao extends BaseMapper<ArticleCheckContent> {

    List<ArticleCheckVO> listArticleErrorById(ArticleCheckQuery query);
    /**
     * 分页查询文章检查结果
     *
     * @param page 分页参数
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<ArticleContentCheckVO> pageArticleChecks(IPage<ArticleContentCheckVO> page, @Param("query") ArticleCheckQuery query);

} 