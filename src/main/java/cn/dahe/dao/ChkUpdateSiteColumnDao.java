package cn.dahe.dao;

import cn.dahe.entity.ChkUpdateSiteColumn;
import cn.dahe.query.ChkUpdateSiteColumnQuery;
import cn.dahe.vo.ChkUpdateSiteColumnVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 栏目更新检查Dao - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Mapper
public interface ChkUpdateSiteColumnDao extends BaseMapper<ChkUpdateSiteColumn> {

    // ==================== 栏目更新检查概览 ====================

    /**
     * 获取栏目更新检查概览统计
     *
     * @param query 查询参数
     * @return 统计数据
     */
    Map<String, Object> getOverviewStatistics(@Param("query") ChkUpdateSiteColumnQuery query);

    // ==================== 栏目更新检查记录 ====================

    /**
     * 分页查询栏目更新检查记录（包含扩展信息）
     *
     * @param page 分页参数
     * @param query 查询参数
     * @return 分页结果
     */
    IPage<ChkUpdateSiteColumnVO> selectPageWithExtInfo(
            Page<ChkUpdateSiteColumnVO> page,
            @Param("query") ChkUpdateSiteColumnQuery query
    );

    /**
     * 根据ID获取栏目更新检查详情
     *
     * @param id ID
     * @return 详情
     */
    ChkUpdateSiteColumnVO selectDetailById(@Param("id") Long id);

}
