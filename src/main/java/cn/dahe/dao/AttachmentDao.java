package cn.dahe.dao;

import cn.dahe.entity.Attachment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *
 */
@Mapper
public interface AttachmentDao extends BaseMapper<Attachment> {
    /**
     * 更新附件的上报信息ID
     *
     * @param ids      附件id串
     * @param reportId 上报信息ID
     */
    void updateReporterIdByIds(@Param("ids") String[] ids, @Param("reportId") int reportId);
}