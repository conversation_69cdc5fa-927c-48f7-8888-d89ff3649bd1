package cn.dahe.dao;

import cn.dahe.entity.CheckErrorType;
import cn.dahe.vo.check.BaseErrorTypeVO;
import cn.dahe.vo.check.FirstLevelErrorTypeVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 错误类型Dao
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Mapper
public interface CheckErrorTypeDao extends BaseMapper<CheckErrorType> {
    
    /**
     * 获取错误类型树形结构
     * 
     * @return 错误类型树
     */
    List<FirstLevelErrorTypeVO> getTypeTree();

    List<BaseErrorTypeVO> listTypeName();
} 