package cn.dahe.dao;

import cn.dahe.dto.ArticleCheckDto;
import cn.dahe.dto.ArticleErrorStatsDto;
import cn.dahe.entity.ArticleCheck;
import cn.dahe.query.ArticleCheckQuery;
import cn.dahe.vo.check.ArticleCheckVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 内容错误详情Dao
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Mapper
public interface ArticleCheckDao extends BaseMapper<ArticleCheck> {

    /**
     * 统计指定网站在指定时间范围内的错误文章数量和级别分布。
     * 按文章数量返回
     *
     * @param webIdList      网站ID列表，用于筛选指定的网站。
     * @param errorLevelList 错误级别列表，用于筛选不同严重级别的错误（如提示、警告、严重）。
     * @param pubBeginTime   发布时间起始时间（格式：yyyy-MM-dd HH:mm:ss），为空时表示不限制起始时间。
     * @param pubEndTime     发布时间结束时间（格式：yyyy-MM-dd HH:mm:ss），为空时表示不限制结束时间。
     * @return 返回文章错误统计数据对象，包含错误数量、各级别分布等信息。
     */
    ArticleErrorStatsDto countErrorArticleStats(
            @Param("webIdList") List<Integer> webIdList,
            @Param("errorLevelList") List<Integer> errorLevelList,
            @Param("pubBeginTime") Date pubBeginTime,
            @Param("pubEndTime") Date pubEndTime
    );

    /**
     * 统计指定网站在指定时间范围内的错误记录数量和级别分布。
     * 按错误数量返回
     *
     * @param webIdList      网站ID列表，用于筛选指定的网站。
     * @param errorLevelList 错误级别列表，用于筛选不同严重级别的错误（如提示、警告、严重）。
     * @param pubBeginTime   发布时间起始时间（格式：yyyy-MM-dd HH:mm:ss），为空时表示不限制起始时间。
     * @param pubEndTime     发布时间结束时间（格式：yyyy-MM-dd HH:mm:ss），为空时表示不限制结束时间。
     * @return 返回错误记录统计数据对象，包含错误数量、各级别分布等信息。
     */
    ArticleErrorStatsDto countErrorRecordStats(
            @Param("webIdList") List<String> webIdList,
            @Param("errorLevelList") List<String> errorLevelList,
            @Param("pubBeginTime") String pubBeginTime,
            @Param("pubEndTime") String pubEndTime
    );


    /**
     * 统计指定网站在指定时间范围内的错误记录数量和级别分布。
     * 按网站数量返回
     *
     * @param webIdList      网站ID列表，用于筛选指定的网站。
     * @param errorLevelList 错误级别列表，用于筛选不同严重级别的错误（如提示、警告、严重）。
     * @param pubBeginTime   发布时间起始时间（格式：yyyy-MM-dd HH:mm:ss），为空时表示不限制起始时间。
     * @param pubEndTime     发布时间结束时间（格式：yyyy-MM-dd HH:mm:ss），为空时表示不限制结束时间。
     * @return 返回错误记录统计数据对象，包含错误数量、各级别分布等信息。
     */
    ArticleErrorStatsDto countErrorWebsiteStats(
            @Param("webIdList") List<String> webIdList,
            @Param("errorLevelList") List<String> errorLevelList,
            @Param("pubBeginTime") String pubBeginTime,
            @Param("pubEndTime") String pubEndTime
    );


    /**
     * 获取文章错误详情（包含错误类型名称）
     *
     * @param id 错误记录ID
     * @return 错误详情DTO
     */
    ArticleCheckDto getArticleCheckDetail(Long id);

    /**
     * 获取文章的错误列表
     *
     * @param articleId   文章ID
     * @param query
     * @return 错误列表
     */
    List<ArticleCheckVO> getArticleCheckList(@Param("articleId") Long articleId,
                                             @Param("query") ArticleCheckQuery query);
} 