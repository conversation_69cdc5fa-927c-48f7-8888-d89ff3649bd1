package cn.dahe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文章排序类型枚举
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Getter
@AllArgsConstructor
public enum ArticleSortTypeEnum {
    /**
     * 按发布时间降序（默认）
     */
    PUB_TIME_DESC(1, "pub_time", "desc", "发布时间降序"),

    /**
     * 按发布时间升序
     */
    PUB_TIME_ASC(2, "pub_time", "asc", "发布时间升序"),

    /**
     * 按检测时间降序
     */
    CHECK_TIME_DESC(3, "check_time", "desc", "检测时间降序"),

    /**
     * 按检测时间升序
     */
    CHECK_TIME_ASC(4, "check_time", "asc", "检测时间升序");

    /**
     * 排序类型ID
     */
    private final Integer code;

    /**
     * 排序字段
     */
    private final String field;

    /**
     * 排序方向
     */
    private final String direction;

    /**
     * 描述
     */
    private final String description;

    /**
     * 获取默认排序类型
     */
    public static ArticleSortTypeEnum getDefault() {
        return PUB_TIME_DESC;
    }

    /**
     * 根据code获取枚举
     */
    public static ArticleSortTypeEnum getByCode(Integer code) {
        if (code == null) {
            return getDefault();
        }
        for (ArticleSortTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return getDefault();
    }
} 