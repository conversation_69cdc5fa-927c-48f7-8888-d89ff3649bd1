package cn.dahe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文章错误位置枚举
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Getter
@AllArgsConstructor
public enum ArticleLocationEnum {
    
    CONTENT(0, "正文"),
    TITLE(1, "标题");
    
    /**
     * 位置编码
     */
    private final Integer code;
    
    /**
     * 位置描述
     */
    private final String description;
    
    /**
     * 根据编码获取枚举
     */
    public static ArticleLocationEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ArticleLocationEnum location : values()) {
            if (location.getCode().equals(code)) {
                return location;
            }
        }
        return null;
    }
} 