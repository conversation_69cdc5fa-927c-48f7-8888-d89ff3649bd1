package cn.dahe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检查策略枚举
 */
@Getter
@AllArgsConstructor
public enum CheckStrategyEnum {

    RECALL(0, "查全"),
    PRECISION(1, "查准");

    private final int code;
    private final String desc;

    public static String getDesc(Integer code) {
        if (code == null) {
            return RECALL.getDesc();
        }
        for (CheckStrategyEnum value : values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return RECALL.getDesc();
    }
} 