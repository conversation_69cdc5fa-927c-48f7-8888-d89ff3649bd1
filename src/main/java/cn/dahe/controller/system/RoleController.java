package cn.dahe.controller.system;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.common.aop.role.Logical;
import cn.dahe.common.aop.role.RequiresRoles;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.Role;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.query.RoleQuery;
import cn.dahe.service.RoleService;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 */
@RestController
@RequestMapping("/pro/role")
@AllArgsConstructor
@Tag(name= "角色模块")
public class RoleController {
    private final RoleService roleService;

    //分页
    @PostMapping("list")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    public Result<PageResult<Role>> page(RoleQuery query) {
        return Result.ok(roleService.page(query));
    }

    //详情
    @PostMapping("id")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    public Result<Role> get(String id) {
        return Result.ok(roleService.getById(id));
    }

    //添加
    @PostMapping("save")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "添加", type = OperateTypeEnum.UPDATE)
    public Result<String> save(Role vo, String permissionIds, @CurrentUser LoginUserVO user) {
        return roleService.save(vo, permissionIds, user);
    }

    //修改
    @PostMapping("update")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "修改", type = OperateTypeEnum.UPDATE)
    public Result<String> update(Role vo, String permissionIds, String id) {
        return roleService.update(id, permissionIds, vo);
    }

    //更新状态
    @PostMapping("update-status")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "更新状态", type = OperateTypeEnum.UPDATE)
    public Result<String> updateStatus(String id) {
        return roleService.updateStatus(id);
    }
}