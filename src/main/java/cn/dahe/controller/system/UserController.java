package cn.dahe.controller.system;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.common.aop.role.Logical;
import cn.dahe.common.aop.role.RequiresRoles;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.dto.Result;
import cn.dahe.entity.User;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.query.UserQuery;
import cn.dahe.service.RoleService;
import cn.dahe.service.UserService;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023-07-05
 */
@RestController
@RequestMapping("/pro/user")
@AllArgsConstructor
@Tag(name = "用户管理模块")
public class UserController {

    @Resource
    private UserService userService;

    @Resource
    private RoleService roleService;

    @Operation(summary = "获取当前登录用户信息")
    @PostMapping("current")
    public Result getCurrentUser(@Parameter(hidden = true) @CurrentUser LoginUserVO loginUser) {
        return Result.ok(loginUser);
    }

    @Operation(summary = "获取用户列表")
    @PostMapping("list")
    public Result list(@Parameter(description = "用户查询条件") UserQuery userQuery) throws IOException {
        return Result.ok(userService.page(userQuery));
    }

    @Operation(summary = "获取指定城市的用户列表")
    @PostMapping("list-city")
    public Result listByCityId(@Parameter(description = "城市ID") @RequestParam("") String cityId) {
        return Result.ok(userService.listByCityIds(cityId));
    }

    @Operation(summary = "获取指定角色的用户列表")
    @PostMapping("list-role")
    public Result listRole(
            @Parameter(description = "用户查询条件") UserQuery userQuery,
            @Parameter(description = "角色ID") @RequestParam(defaultValue = "0") String roleId) {
        return Result.ok(userService.pageRole(userQuery, roleId));
    }

    @Operation(summary = "删除指定角色下的用户")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @PostMapping("delete-role")
    @OperateLog(name = "删除-某角色下的某用户", type = OperateTypeEnum.UPDATE)
    public Result listRole(
            @Parameter(description = "角色ID") String roleId,
            @Parameter(description = "用户ID") String userId) {
        return roleService.removeByUserId(userId, roleId);
    }

    @Operation(summary = "分配角色")
    @PostMapping("assign-role")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "分配角色", type = OperateTypeEnum.UPDATE)
    public Result assignRole(
            @Parameter(description = "用户ID") String userId,
            @Parameter(description = "角色ID列表，多个以逗号分隔") String roleIds) {
        return roleService.assignRole(userId, roleIds);
    }

    @Operation(summary = "添加用户")
    @PostMapping("save")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "添加用户", type = OperateTypeEnum.UPDATE)
    public Result save(
            @Parameter(description = "用户信息") User user,
            @Parameter(description = "角色ID列表，多个以逗号分隔") @RequestParam(defaultValue = "") String roleIds,
            @Parameter(hidden = true) @CurrentUser LoginUserVO loginUser) {
        return userService.save(user, roleIds, loginUser);
    }

    @Operation(summary = "修改用户")
    @PostMapping("update")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "修改用户", type = OperateTypeEnum.UPDATE)
    public Result update(
            @Parameter(description = "用户信息") User user,
            @Parameter(description = "角色ID列表，多个以逗号分隔") @RequestParam(defaultValue = "") String roleIds,
            @Parameter(hidden = true) @CurrentUser LoginUserVO loginUser) {
        return userService.update(user, roleIds, loginUser);
    }

    @Operation(summary = "修改用户状态")
    @PostMapping("update-status")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "修改状态", type = OperateTypeEnum.UPDATE)
    public Result updateStatus(
            @Parameter(description = "用户ID") @RequestParam(defaultValue = "") String userId,
            @Parameter(hidden = true) @CurrentUser LoginUserVO loginUser) {
        return userService.updateStatus(userId, loginUser);
    }

    @Operation(summary = "修改当前用户密码")
    @PostMapping("update-my-pwd")
    @OperateLog(name = "修改自己的密码", type = OperateTypeEnum.UPDATE)
    public Result updateMyPwd(
            @Parameter(description = "新密码") @RequestParam(defaultValue = "") String newPwd,
            @Parameter(description = "旧密码") @RequestParam(defaultValue = "") String oldPwd,
            @Parameter(hidden = true) @CurrentUser LoginUserVO loginUser) {
        return userService.updateMyPwd(String.valueOf(loginUser.getUserId()), newPwd, oldPwd);
    }

    @Operation(summary = "重置用户密码")
    @PostMapping("reset-pwd")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER}, logical = Logical.OR)
    @OperateLog(name = "重置密码", type = OperateTypeEnum.UPDATE)
    public Result updateResetPwd(
            @Parameter(description = "用户ID") @RequestParam(defaultValue = "") String userId) {
        return userService.updateResetPwd(userId);
    }
}
