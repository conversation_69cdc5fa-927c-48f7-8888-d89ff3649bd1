package cn.dahe.controller.system;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.common.aop.role.RequiresRoles;
import cn.dahe.common.constants.RoleConstants;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.Permission;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.query.PermissionQuery;
import cn.dahe.service.PermissionService;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 */
@RestController
@RequestMapping("/pro/permission")
@AllArgsConstructor
@Tag(name= "权限模块")
public class PermissionController {
    private final PermissionService permissionService;


    //列表
    @PostMapping("list")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER})
    public Result<PageResult<Permission>> page(PermissionQuery query) {
        return Result.ok(permissionService.page(query));
    }

    //详情
    @PostMapping("id")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER})
    public Result<Permission> get(String id) {
        return Result.ok(permissionService.getById(id));
    }

    //添加
    @PostMapping("save")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER})
    @OperateLog(name = "添加", type = OperateTypeEnum.UPDATE)
    public Result<String> save(Permission vo, @CurrentUser LoginUserVO user) {
        return permissionService.save(vo, user);
    }

    //修改
    @PostMapping("update")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER})
    @OperateLog(name = "修改", type = OperateTypeEnum.UPDATE)
    public Result<String> update(Permission vo, String id) {
        return permissionService.update(id, vo);
    }

    //修改状态
    @PostMapping("update-status")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER})
    @OperateLog(name = "修改状态", type = OperateTypeEnum.UPDATE)
    public Result<String> updateStatus(String id) {
        return permissionService.updateStatus(id);
    }


    //原始权限树
    @PostMapping("tree")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER})
    public Result getTree() {
        return Result.ok(permissionService.getTree());
    }


    //角色权限树
    @PostMapping("tree-role")
    @RequiresRoles(value = {RoleConstants.ADMIN_SUPER})
    public Result getRoleTree(Integer roleId) {
        return Result.ok(permissionService.getRoleTree(roleId));
    }


}