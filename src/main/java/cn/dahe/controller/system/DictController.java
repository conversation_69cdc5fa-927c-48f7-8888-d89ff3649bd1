package cn.dahe.controller.system;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.dto.Result;
import cn.dahe.entity.Dict;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.query.DictQuery;
import cn.dahe.service.DictService;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024-02-21
 */
@Slf4j
@RestController
@RequestMapping("/pro/dict/")
@AllArgsConstructor
@Tag(name = "数据字典")
public class DictController {

    private DictService dictService;


    //分页获取
    @PostMapping({"list"})
    public Result page(DictQuery dictQuery) {
        return Result.ok(dictService.page(dictQuery));
    }


    //字典树-媒体介质
    @PostMapping({"list-tree-media-by-id"})
    public Result listTreeMedia(String dictIdProject) {
        return dictService.listMediaTreelByProject(dictIdProject);
    }

    //字典树-专门媒体介质
    @PostMapping({"list-tree-media-by-id-special"})
    public Result listTreeMediaZhuanMen(String dictIdGenre) {
        return dictService.listMediaTreelByProjectSpecial(dictIdGenre);
    }


    //字典树
    @PostMapping({"list-tree-by-id"})
    public Result listTreeById(@RequestParam("") String id, @RequestParam(defaultValue = "", required = false) String self) {
        return dictService.listDictTreelById(id);
    }


    @PostMapping({"list-tree-project"})
    public Result listTreeProject(@CurrentUser LoginUserVO loginUserVO) {
        return Result.ok(dictService.listDictTreelProject(loginUserVO));
    }

    // 根据类型获取参评项目
    @PostMapping({"list-tree-project-by-type"})
    public Result listTreeProjectByType(String type, @CurrentUser LoginUserVO loginUserVO) {
        return Result.ok(dictService.listDictTreelProjectByType(type));
    }

    //添加
    @PostMapping("save")
    @OperateLog(name = "添加", type = OperateTypeEnum.CREATE)
    public Result<String> save(Dict vo, @CurrentUser LoginUserVO user) {
        return dictService.save(vo, user);
    }

    //修改
    @PostMapping("update")
    @OperateLog(name = "修改", type = OperateTypeEnum.UPDATE)
    public Result<String> update(String id, Dict vo, @CurrentUser LoginUserVO user) {
        return dictService.update(id, vo, user);
    }

    //禁用/启用
    @PostMapping("update-status")
    @OperateLog(name = "更新状态", type = OperateTypeEnum.UPDATE)
    public Result<String> updateStatusDisable(String id, @RequestParam(required = false, defaultValue = "0") int status) {
        return dictService.updateStatus(id, status);
    }


}
