package cn.dahe.controller.system;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.common.constants.StatusConstants;
import cn.dahe.dto.Result;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.query.DepartmentQuery;
import cn.dahe.service.DepartmentService;
import cn.dahe.vo.DepartmentVO;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 */
@Slf4j
@RestController
@RequestMapping("/pro/dep/")
@AllArgsConstructor
@Tag(name = "部门管理")
public class DepartmentController {

    private DepartmentService departmentService;


    //分页获取
    @PostMapping({"list"})
    public Result page(DepartmentQuery departmentQuery) {
        return Result.ok(departmentService.page(departmentQuery));
    }

    //获取所有
    @PostMapping({"list-all"})
    public Result listALL(@RequestParam(defaultValue = "") String name) {
        return Result.ok(departmentService.getByStatusAndName(name, StatusConstants.COMMON_NORMAL));
    }

    //添加用户
    @PostMapping("save")
    @OperateLog(name = "添加部门", type = OperateTypeEnum.UPDATE)
    public Result save(DepartmentVO departmentVO, @CurrentUser LoginUserVO loginUser) {
        return departmentService.save(departmentVO, loginUser);
    }

    //修改用户
    @PostMapping("update")
    @OperateLog(name = "修改部门", type = OperateTypeEnum.UPDATE)
    public Result update(String id, DepartmentVO departmentVO, @CurrentUser LoginUserVO loginUser) {
        return departmentService.update(id, departmentVO, loginUser);
    }

}