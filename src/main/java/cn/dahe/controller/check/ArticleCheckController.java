package cn.dahe.controller.check;

import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.query.ArticleCheckQuery;
import cn.dahe.service.ArticleCheckContentService;
import cn.dahe.service.ArticleCheckService;
import cn.dahe.vo.check.ArticleCheckVO;
import cn.dahe.vo.check.ArticleContentCheckVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文章检查控制器
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Tag(name = "内容巡检")
@RestController
@RequestMapping("/pro/article/check")
public class ArticleCheckController {

    @Resource
    private ArticleCheckService articleCheckService;
    @Resource
    private ArticleCheckContentService articleCheckContentService;

    @Operation(summary = "分页查询内容检查结果")
    @PostMapping(value = "/page")
    public Result<PageResult<ArticleContentCheckVO>> pageArticleChecks(ArticleCheckQuery query) {
        IPage<ArticleContentCheckVO> result = articleCheckContentService.pageArticleChecks(query);
        return Result.ok(PageResult.page(result));
    }

    @Operation(summary = "错词审核-驳回")
    @PostMapping("audit-reject")
    public Result<String> updateAuditStatusReject(@RequestParam Long articleId,
                                             @Parameter(description = "检查ID列表，逗号分隔") @RequestParam List<Long> checkIds) {
        Boolean success = articleCheckService.updateErrorAuditStatus(articleId, checkIds, AuditStatusEnum.REJECT);
        return success ? Result.ok("操作成功") : Result.error("操作失败");
    }

    @Operation(summary = "错词审核-同意")
    @PostMapping("audit-pass")
    public Result<String> updateAuditStatusPass(@RequestParam Long articleId,
                                           @Parameter(description = "检查ID列表，逗号分隔") @RequestParam List<Long> checkIds) {
        Boolean success = articleCheckService.updateErrorAuditStatus(articleId, checkIds, AuditStatusEnum.PASS);
        return success ? Result.ok("操作成功") : Result.error("操作失败");
    }

    @Operation(summary = "错词审核-撤回")
    @PostMapping("audit-withdraw")
    public Result<String> updateAuditStatusWithdraw(@RequestParam Long articleId,
                                               @Parameter(description = "检查ID列表，逗号分隔") @RequestParam List<Long> checkIds) {
        Boolean success = articleCheckService.updateErrorAuditStatus(articleId, checkIds, AuditStatusEnum.WAITING_FOR_REVIEW);
        return success ? Result.ok("操作成功") : Result.error("操作失败");
    }

    @Operation(summary = "快照获取文章错误列表-TODO")
    @GetMapping("/list/{articleId}")
    public Result<List<ArticleCheckVO>> listArticleChecks(@PathVariable("articleId") Long articleId,ArticleCheckQuery query) {
        return Result.ok(articleCheckService.getArticleCheckList(articleId, query));
    }
}