package cn.dahe.controller.check;

import cn.dahe.dto.Result;
import cn.dahe.entity.CheckTask;
import cn.dahe.service.CheckTaskService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 内容检查任务Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/check/task")
public class CheckTaskController {

    @Resource
    private CheckTaskService checkTaskService;

    /**
     * 创建检查任务
     */
    @PostMapping
    public Result<CheckTask> create(@RequestBody CheckTask task) {
        checkTaskService.save(task);
        return Result.ok(task);
    }

    /**
     * 获取任务详情
     */
    @GetMapping("/{id}")
    public Result<CheckTask> getById(@PathVariable Long id) {
        return Result.ok(checkTaskService.getById(id));
    }

    /**
     * 更新任务状态
     */
    @PutMapping("/{id}")
    public Result<Boolean> update(@PathVariable Long id, @RequestBody CheckTask task) {
        task.setId(id);
        return Result.ok(checkTaskService.updateById(task));
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.ok(checkTaskService.removeById(id));
    }
} 