package cn.dahe.controller.check;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.aop.log.OperateLog;
import cn.dahe.dto.ArticleDto;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.enums.AuditStatusEnum;
import cn.dahe.enums.OperateTypeEnum;
import cn.dahe.query.ArticleQuery;
import cn.dahe.service.ArticleService;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 采集文章Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Tag(name = "文章管理")
@RestController
@RequestMapping("/pro/article")
public class ArticleController {

    @Resource
    private ArticleService articleService;

    @Operation(summary = "分页查询内容检查结果（已弃用）")
    @PostMapping("page")
    public Result<PageResult<ArticleDto>> page(ArticleQuery query, @CurrentUser LoginUserVO user) {
        return Result.ok(articleService.page(query));
    }


    @Operation(summary = "文章审核-驳回")
    @PostMapping("audit-reject")
    @OperateLog(name = "审核驳回", type = OperateTypeEnum.UPDATE)
    public Result<String> updateStatusReject(
            @Parameter(description = "文章ID列表，逗号分隔") @RequestParam List<Long> articleIds) {
        Boolean success = articleService.updateArticleAuditStatus(articleIds, AuditStatusEnum.REJECT);
        return success ? Result.ok("操作成功") : Result.error("操作失败");
    }


    @Operation(summary = "文章审核-通过")
    @PostMapping("audit-pass")
    @OperateLog(name = "审核通过", type = OperateTypeEnum.UPDATE)
    public Result<String> updateStatusPass(
            @Parameter(description = "文章ID列表，逗号分隔") @RequestParam List<Long> articleIds) {
        Boolean success = articleService.updateArticleAuditStatus(articleIds, AuditStatusEnum.PASS);
        return success ? Result.ok("操作成功") : Result.error("操作失败");
    }


    @Operation(summary = "文章审核-撤回")
    @PostMapping("audit-withdraw")
    @OperateLog(name = "撤回审核", type = OperateTypeEnum.UPDATE)
    public Result<String> updateStatusWithdraw(
            @Parameter(description = "文章ID列表，逗号分隔") @RequestParam List<Long> articleIds) {
        Boolean success = articleService.updateArticleAuditStatus(articleIds, AuditStatusEnum.WAITING_FOR_REVIEW);
        return success ? Result.ok("操作成功") : Result.error("操作失败");
    }


    @Operation(summary = "处置接口")
    @PostMapping("update-disposal-status")
    @OperateLog(name = "处置接口", type = OperateTypeEnum.UPDATE)
    public Result<String> updateDisposalStatus(@RequestParam(defaultValue = "") String articleIds,
                                               @RequestParam(defaultValue = "0") int disposalStatus,
                                               @RequestParam(defaultValue = "") String disposalRemark,
                                               @CurrentUser LoginUserVO loginUserVO) {
        return articleService.updateDisposalStatus(articleIds, disposalStatus, disposalRemark, loginUserVO);
    }


    // 整改下发

}