package cn.dahe.controller.check;

import cn.dahe.dto.Result;
import cn.dahe.entity.CheckErrorLevel;
import cn.dahe.service.CheckErrorLevelService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 内容错误等级Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Tag(name = "错误等级errorLevel管理")
@RestController
@RequestMapping("/check/error/level")
public class CheckErrorLevelController {

    @Resource
    private CheckErrorLevelService checkErrorLevelService;

    /**
     * 获取所有错误等级
     */
    @GetMapping("/list")
    public Result<List<CheckErrorLevel>> list() {
        return Result.ok(checkErrorLevelService.list());
    }

    /**
     * 获取错误等级详情
     */
    @GetMapping("/{id}")
    public Result<CheckErrorLevel> getById(@PathVariable Integer id) {
        return Result.ok(checkErrorLevelService.getById(id));
    }

    /**
     * 新增错误等级
     */
    @PostMapping
    public Result<CheckErrorLevel> save(@RequestBody CheckErrorLevel level) {
        checkErrorLevelService.save(level);
        return Result.ok(level);
    }

    /**
     * 更新错误等级
     */
    @PutMapping("/{id}")
    public Result<Boolean> update(@PathVariable Integer id, @RequestBody CheckErrorLevel level) {
        level.setId(id);
        return Result.ok(checkErrorLevelService.updateById(level));
    }

    /**
     * 删除错误等级
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Integer id) {
        return Result.ok(checkErrorLevelService.removeById(id));
    }
} 