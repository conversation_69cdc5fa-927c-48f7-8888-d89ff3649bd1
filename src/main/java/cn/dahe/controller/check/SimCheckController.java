package cn.dahe.controller.check;

import cn.dahe.dto.ArticleCheckContentDto;
import cn.dahe.dto.ArticleSaveDto;
import cn.dahe.dto.ContentCheckResultDto;
import cn.dahe.dto.Result;
import cn.dahe.entity.Article;
import cn.dahe.entity.ArticleCheck;
import cn.dahe.service.ArticleCheckContentService;
import cn.dahe.service.ArticleCheckService;
import cn.dahe.service.ArticleService;
import cn.dahe.service.SimCheckService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "模拟校对")
@Slf4j
@RestController
//  修改前缀，回避登录限制
@RequestMapping("/sim/check")
public class SimCheckController {

    @Resource
    private ArticleService articleService;
    @Resource
    private SimCheckService simCheckService;
    @Resource
    private ArticleCheckContentService articleCheckContentService;


    @Resource
    private ArticleCheckService articleCheckService;


    @Data
    public static class ContentCheckRequest {
        private String title;
        private String content;
    }


    @Operation(summary = "模拟文章校对")
    @PostMapping("/check")
    public Result<List<ArticleCheck>> simulateCheck(
            @Parameter(description = "校对请求") ContentCheckRequest request) {
        List<ArticleCheck> results = simCheckService.simulateCheck(request.getTitle(), request.getContent());
        return Result.ok(results);
    }


    @Operation(summary = "模拟推送文章")
    @PostMapping("/push")
    public Result<SimCheckService.PushArticle> simulatePush() {
        SimCheckService.PushArticle article = simCheckService.pushRandomArticle();
        // TODO 调用接收接口
        return receivePush(article);
    }

    @Operation(summary = "接收推送文章")
    @PostMapping("/receive")
    public Result<SimCheckService.PushArticle> receivePush(@Parameter(description = "推送的文章") SimCheckService.PushArticle article) {
        // 1. 保存Article
        ArticleSaveDto saveDTO = new ArticleSaveDto()
                .setWebsiteId(article.getWebsiteId())
                .setTitle(article.getTitle())
                .setUrl(article.getUrl())
                .setPubTime(article.getPubTime());
        Article newArticle = articleService.savePushArticle(saveDTO);
        Long articleId = newArticle.getId();
        // 2. 处理并保存文章内容（即使校对失败也能保留内容）
        ArticleCheckContentDto initCheckContentDto = articleCheckService.initPushArticle(
                articleId,
                article.getTitle(),
                article.getContent()
        );

        // 3. 执行内容校对
        ContentCheckResultDto checkResult = articleCheckService.executeContentCheck(
                initCheckContentDto.getCleanedTitle(),
                initCheckContentDto.getCleanedContent()
        );
        // 4. 处理校对结果
        articleCheckService.processCheckResults(articleId, initCheckContentDto, checkResult);
        return Result.ok(article);
    }


}
