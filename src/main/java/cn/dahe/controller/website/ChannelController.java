package cn.dahe.controller.website;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.dto.Result;
import cn.dahe.entity.Channel;
import cn.dahe.query.ChannelQuery;
import cn.dahe.service.ChannelService;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 网站信息管理
 */
@RestController
@RequestMapping("/pro/channel")
@AllArgsConstructor
@Tag(name = "网站管理模块")
public class ChannelController {

    @Resource
    private ChannelService channelService;

    //分页
    @PostMapping("list")
    public Result page(ChannelQuery query) {
        return Result.ok(channelService.page(query));
    }


    //添加
    @PostMapping("save")
    public Result save(Channel vo, @CurrentUser LoginUserVO user) {
        return channelService.save(vo, user);
    }

    //添加
    @PostMapping("update")
    public Result update(Channel vo, @CurrentUser LoginUserVO user) {
        return channelService.update(vo, user);
    }

    //修改状态
    @PostMapping("update-status")
    public Result updateStatus(@RequestParam(defaultValue = "") String id,
                               @CurrentUser LoginUserVO user) {
        return channelService.updateStatus(id, user);
    }

}
