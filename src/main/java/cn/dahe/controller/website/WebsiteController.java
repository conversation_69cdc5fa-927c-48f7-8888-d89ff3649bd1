package cn.dahe.controller.website;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.dto.Result;
import cn.dahe.entity.Website;
import cn.dahe.query.WebsiteQuery;
import cn.dahe.service.WebsiteService;
import cn.dahe.vo.LoginUserVO;
import cn.dahe.vo.WebsiteVO;
import cn.dahe.vo.WebsiteGroupVO;
import cn.dahe.service.WebsiteGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 网站信息管理
 */
@RestController
@RequestMapping("/pro/web-site")
@AllArgsConstructor
@Tag(name= "网站管理模块")
public class WebsiteController {

    @Resource
    private WebsiteService webSiteService;

    @Resource
    private WebsiteGroupService websiteGroupService;

    @Operation(summary = "分页查询网站列表")
    @PostMapping("list")
    public Result page(WebsiteQuery query) {
        return Result.ok(webSiteService.page(query));
    }

    //分页-按照状态
    @PostMapping("list-by-status")
    public Result listByStatus(int status) {
        return Result.ok(webSiteService.listByStatus(status));
    }

    //添加
    @PostMapping("save")
    public Result save(Website vo, @CurrentUser LoginUserVO user) {
        return webSiteService.save(vo, user);
    }

    //添加
    @PostMapping("update")
    public Result update(Website vo, @CurrentUser LoginUserVO user) {
        return webSiteService.update(vo, user);
    }

    //修改状态
    @PostMapping("update-status")
    public Result updateStatus(@RequestParam(defaultValue = "") String id,
                               @CurrentUser LoginUserVO user) {
        return webSiteService.updateStatus(id, user);
    }


    @Operation(summary = "查询所有可用站点列表")
    @PostMapping("total")
    public Result<List<WebsiteVO>> total() {
        return Result.ok(webSiteService.listTotal());
    }

    @Operation(summary = "按分组查询所有可用站点列表")
    @PostMapping("group/total")
    public Result<List<WebsiteGroupVO>> listByGroup() {
        return Result.ok(websiteGroupService.listWithWebsites());
    }

}
