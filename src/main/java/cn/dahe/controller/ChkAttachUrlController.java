package cn.dahe.controller;

import cn.dahe.common.annotation.ApiAuth;
import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.ChkAttachUrl;
import cn.dahe.query.ChkAttachUrlQuery;
import cn.dahe.service.ChkAttachUrlService;
import cn.dahe.vo.ChkAttachUrlVO;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 附件检查Controller - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/pro/chk-attach-url")
@AllArgsConstructor
@Tag(name = "附件检查管理")
public class ChkAttachUrlController {

    @Resource
    private ChkAttachUrlService chkAttachUrlService;

    // ==================== 附件检查记录 ====================

    @Operation(summary = "分页查询附件检查记录", description = "获取附件检查记录列表，包含网站名称、来源网站、来源页面等信息")
    @PostMapping("page")
    public Result<PageResult<ChkAttachUrlVO>> page(
            @Parameter(description = "查询参数") ChkAttachUrlQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkAttachUrlService.page(query));
    }

    @Operation(summary = "获取附件检查详情", description = "根据ID获取附件检查详细信息")
    @PostMapping("detail")
    public Result<ChkAttachUrlVO> detail(
            @Parameter(description = "记录ID") @RequestParam Long id,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkAttachUrlService.get(id));
    }

    // ==================== 数据导出 ====================

    @Operation(summary = "导出附件检查记录", description = "导出附件检查记录到Excel")
    @GetMapping("export")
    public void export(
            @Parameter(description = "站点类型：1网站，2分组") @RequestParam(required = false, defaultValue = "1") int siteType,
            @Parameter(description = "站点值") @RequestParam(required = false) String siteValue,
            @Parameter(description = "排序时间类型：1发布时间，2检测时间") @RequestParam(required = false, defaultValue = "2") int timeType,
            @Parameter(description = "排序值：asc升序，desc降序") @RequestParam(required = false, defaultValue = "desc") String orderValue,
            @Parameter(description = "排序开始时间") @RequestParam(required = false) String startTime,
            @Parameter(description = "排序结束时间") @RequestParam(required = false) String endTime,
            @CurrentUser LoginUserVO user,
            HttpServletResponse response) throws IOException {

        ChkAttachUrlQuery query = new ChkAttachUrlQuery();
        query.setSiteType(siteType);
        query.setSiteValue(siteValue);
        query.setTimeType(timeType);
        query.setOrderValue(orderValue);
        query.setStartTime(startTime);
        query.setEndTime(endTime);

        chkAttachUrlService.export(query, user, response);
    }

    @Operation(summary = "接收推送附件数据", description = "被动接收采集中心推送的附件检查数据并初始化表数据")
    @ApiAuth(value = "collection_center", description = "采集中心推送数据认证")
    @PostMapping("receive-push-data")
    public Result<String> receivePushData(
            @Parameter(description = "推送的附件数据") @RequestBody String pushData) {
        log.info("接收到采集中心推送的附件检查数据");

        try {
            // TODO: 实现接收推送数据的逻辑
            // 1. 解析推送的JSON数据
            // 2. 验证数据格式和完整性
            // 3. 转换为ChkAttachUrl实体
            // 4. 批量插入或更新数据库
            // 5. 设置检测状态和时间

            log.info("接收推送附件检查数据成功，数据长度：{}", pushData != null ? pushData.length() : 0);
            return Result.ok("接收推送数据成功，共处理0条记录");
        } catch (Exception e) {
            log.error("接收推送附件检查数据失败", e);
            return Result.error("接收推送数据失败：" + e.getMessage());
        }
    }
}
