package cn.dahe.controller;

import cn.dahe.common.annotation.ApiAuth;
import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.ChkUpdateSiteIndex;
import cn.dahe.query.ChkUpdateSiteIndexQuery;
import cn.dahe.service.ChkUpdateSiteIndexService;
import cn.dahe.vo.ChkUpdateSiteIndexVO;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 首页更新检查Controller - 专注于首页更新检查功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/pro/chk-update-site-index")
@AllArgsConstructor
@Tag(name = "首页更新检查")
public class ChkUpdateSiteIndexController {

    @Resource
    private ChkUpdateSiteIndexService chkUpdateSiteIndexService;

    // ==================== 首页更新检查概览 ====================

    @Operation(summary = "获取首页更新检查概览统计", description = "获取检测网站数、更新网站、未更新网站统计数据")
    @PostMapping("overview-statistics")
    public Result<Map<String, Object>> getOverviewStatistics(
            @Parameter(description = "查询参数") ChkUpdateSiteIndexQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteIndexService.getOverviewStatistics(query));
    }

    // ==================== 首页更新检查记录 ====================

    @Operation(summary = "分页查询首页更新检查记录", description = "获取网站首页更新检查记录列表")
    @PostMapping("page")
    public Result<PageResult<ChkUpdateSiteIndexVO>> page(
            @Parameter(description = "查询参数") ChkUpdateSiteIndexQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteIndexService.page(query));
    }

    @Operation(summary = "获取首页更新检查详情", description = "根据ID获取首页更新检查详细信息")
    @PostMapping("detail")
    public Result<ChkUpdateSiteIndexVO> detail(
            @Parameter(description = "记录ID") @RequestParam Long id,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteIndexService.get(id));
    }

    // ==================== 数据导出 ====================

    @Operation(summary = "导出检查记录", description = "导出首页更新检查记录到Excel")
    @GetMapping("export")
    public void export(
            @Parameter(description = "网站名称") @RequestParam(required = false) String websiteName,
            @Parameter(description = "网站地址") @RequestParam(required = false) String websiteIndexUrl,
            @Parameter(description = "分组名称") @RequestParam(required = false) String groupName,
            @Parameter(description = "解析开始时间") @RequestParam(required = false) String parseBeginTime,
            @Parameter(description = "解析结束时间") @RequestParam(required = false) String parseEndTime,
            @Parameter(description = "创建开始时间") @RequestParam(required = false) String createBeginTime,
            @Parameter(description = "创建结束时间") @RequestParam(required = false) String createEndTime,
            @CurrentUser LoginUserVO user,
            HttpServletResponse response) throws IOException {

        ChkUpdateSiteIndexQuery query = new ChkUpdateSiteIndexQuery();
        query.setWebsiteName(websiteName);
        query.setWebsiteIndexUrl(websiteIndexUrl);
        query.setGroupName(groupName);
        query.setParseBeginTime(parseBeginTime);
        query.setParseEndTime(parseEndTime);
        query.setCreateBeginTime(createBeginTime);
        query.setCreateEndTime(createEndTime);

        chkUpdateSiteIndexService.export(query, user, response);
    }

    @Operation(summary = "接收推送首页更新数据", description = "被动接收采集中心推送的首页更新检查数据并初始化表数据")
    @ApiAuth(value = "collection_center", description = "采集中心推送数据认证")
    @PostMapping("receive-push-data")
    public Result<String> receivePushData(
            @Parameter(description = "推送的首页更新数据") @RequestBody String pushData) {
        log.info("接收到采集中心推送的首页更新检查数据");

        try {
            // TODO: 实现接收推送数据的逻辑
            // 1. 解析推送的JSON数据
            // 2. 验证数据格式和完整性
            // 3. 转换为ChkUpdateSiteIndex实体
            // 4. 批量插入或更新数据库
            // 5. 设置检测状态和解析时间

            log.info("接收推送首页更新检查数据成功，数据长度：{}", pushData != null ? pushData.length() : 0);
            return Result.ok("接收推送数据成功，共处理0条记录");
        } catch (Exception e) {
            log.error("接收推送首页更新检查数据失败", e);
            return Result.error("接收推送数据失败：" + e.getMessage());
        }
    }
}
