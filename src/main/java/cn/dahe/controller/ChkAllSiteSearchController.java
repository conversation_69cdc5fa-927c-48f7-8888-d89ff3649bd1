package cn.dahe.controller;

import cn.dahe.common.annotation.ApiAuth;
import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.query.ChkAllSiteSearchQuery;
import cn.dahe.service.ChkAllSiteSearchService;
import cn.dahe.vo.ChkAllSiteSearchVO;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 全站搜索Controller
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@RestController
@RequestMapping("/pro/chk-all-site-search")
@Tag(name = "全站搜索", description = "全站搜索相关接口")
public class ChkAllSiteSearchController {

    @Resource
    private ChkAllSiteSearchService chkAllSiteSearchService;

    @Operation(summary = "分页查询搜索记录",
               description = "支持关键词搜索、站点筛选、内容类型筛选、过滤结果筛选、时间范围筛选等多种搜索条件")
    @PostMapping("page")
    public Result<PageResult<ChkAllSiteSearchVO>> page(
            @Parameter(description = "查询参数") @RequestBody ChkAllSiteSearchQuery query,
            @CurrentUser LoginUserVO user) {
        log.info("用户{}执行全站搜索，关键词：{}，站点类型：{}，站点值：{}",
                user.getUserId(), query.getKeywords(), query.getSiteType(), query.getSiteValue());
        PageResult<ChkAllSiteSearchVO> result = chkAllSiteSearchService.page(query);
        return Result.ok(result);
    }

    @Operation(summary = "接收推送搜索数据", description = "被动接收采集中心推送的全站搜索数据并初始化表数据")
    @ApiAuth(value = "collection_center", description = "采集中心推送数据认证")
    @PostMapping("receive-push-data")
    public Result<String> receivePushData(
            @Parameter(description = "推送的搜索数据") @RequestBody String pushData) {
        log.info("接收到采集中心推送的全站搜索数据");

        try {
            // TODO: 实现接收推送数据的逻辑
            // 1. 解析推送的JSON数据
            // 2. 验证数据格式和完整性
            // 3. 转换为ChkAllSiteSearch实体
            // 4. 批量插入或更新数据库

            log.info("接收推送全站搜索数据成功，数据长度：{}", pushData != null ? pushData.length() : 0);
            return Result.ok("接收推送数据成功，共处理0条记录");
        } catch (Exception e) {
            log.error("接收推送全站搜索数据失败", e);
            return Result.error("接收推送数据失败：" + e.getMessage());
        }
    }
}
