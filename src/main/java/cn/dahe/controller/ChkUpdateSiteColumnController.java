package cn.dahe.controller;

import cn.dahe.common.annotation.ApiAuth;
import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.entity.ChkUpdateSiteColumn;
import cn.dahe.query.ChkUpdateSiteColumnQuery;
import cn.dahe.service.ChkUpdateSiteColumnService;
import cn.dahe.vo.ChkUpdateSiteColumnVO;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 栏目更新检查Controller - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/pro/chk-update-site-column")
@AllArgsConstructor
@Tag(name = "栏目更新检查管理")
public class ChkUpdateSiteColumnController {

    @Resource
    private ChkUpdateSiteColumnService chkUpdateSiteColumnService;

    // ==================== 栏目更新检查概览 ====================
    @Operation(summary = "获取栏目更新检查概览统计",
               description = "获取栏目统计（栏目总数、正常个数、采集异常个数、不检测更新个数）和检测结果统计（检测结果总数、正常个数、严重逾期个数、即将逾期个数）")
    @PostMapping("overview-statistics")
    public Result<Map<String, Object>> getOverviewStatistics(
            @Parameter(description = "查询参数") ChkUpdateSiteColumnQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteColumnService.getOverviewStatistics(query));
    }

    // ==================== 栏目更新检查记录 ====================

    @Operation(summary = "分页查询栏目更新检查记录", description = "获取栏目更新检查记录列表")
    @PostMapping("page")
    public Result<PageResult<ChkUpdateSiteColumnVO>> page(
            @Parameter(description = "查询参数") ChkUpdateSiteColumnQuery query,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteColumnService.page(query));
    }

    @Operation(summary = "获取栏目更新检查详情", description = "根据ID获取栏目更新检查详细信息")
    @PostMapping("detail")
    public Result<ChkUpdateSiteColumnVO> detail(
            @Parameter(description = "记录ID") @RequestParam Long id,
            @CurrentUser LoginUserVO user) {
        return Result.ok(chkUpdateSiteColumnService.get(id));
    }

    // ==================== 数据导出 ====================

    @Operation(summary = "导出栏目更新检查记录", description = "导出栏目更新检查记录到Excel")
    @GetMapping("export")
    public void export(
            @Parameter(description = "栏目名称") @RequestParam(required = false) String columnName,
            @Parameter(description = "栏目分类") @RequestParam(required = false) String columnCategory,
            @Parameter(description = "检查开始时间") @RequestParam(required = false) String checkStartTime,
            @Parameter(description = "检查结束时间") @RequestParam(required = false) String checkEndTime,
            @Parameter(description = "更新开始时间") @RequestParam(required = false) String updateStartTime,
            @Parameter(description = "更新结束时间") @RequestParam(required = false) String updateEndTime,
            @Parameter(description = "网站链接") @RequestParam(required = false) String websiteUrl,
            @Parameter(description = "未更新天数") @RequestParam(required = false) Integer notUpdateDays,
            @Parameter(description = "检查日期") @RequestParam(required = false) String checkDate,
            @CurrentUser LoginUserVO user,
            HttpServletResponse response) throws IOException {

        ChkUpdateSiteColumnQuery query = new ChkUpdateSiteColumnQuery();
        query.setColumnName(columnName);
        query.setColumnCategory(columnCategory);
        query.setCheckStartTime(checkStartTime);
        query.setCheckEndTime(checkEndTime);
        query.setUpdateStartTime(updateStartTime);
        query.setUpdateEndTime(updateEndTime);
        query.setWebsiteUrl(websiteUrl);
        query.setNotUpdateDays(notUpdateDays);
        query.setCheckDate(checkDate);

        chkUpdateSiteColumnService.export(query, user, response);
    }

    @Operation(summary = "接收推送栏目更新数据", description = "被动接收采集中心推送的栏目更新检查数据并初始化表数据")
    @ApiAuth(value = "collection_center", description = "采集中心推送数据认证")
    @PostMapping("receive-push-data")
    public Result<String> receivePushData(
            @Parameter(description = "推送的栏目更新数据") @RequestBody String pushData) {
        log.info("接收到采集中心推送的栏目更新检查数据");

        try {
            // TODO: 实现接收推送数据的逻辑
            // 1. 解析推送的JSON数据
            // 2. 验证数据格式和完整性
            // 3. 转换为ChkUpdateSiteColumn实体
            // 4. 批量插入或更新数据库
            // 5. 设置检测状态和解析时间

            log.info("接收推送栏目更新检查数据成功，数据长度：{}", pushData != null ? pushData.length() : 0);
            return Result.ok("接收推送数据成功，共处理0条记录");
        } catch (Exception e) {
            log.error("接收推送栏目更新检查数据失败", e);
            return Result.error("接收推送数据失败：" + e.getMessage());
        }
    }
}
