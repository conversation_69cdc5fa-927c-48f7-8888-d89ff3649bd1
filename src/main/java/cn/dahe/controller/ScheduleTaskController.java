package cn.dahe.controller;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.common.task.DataPullScheduleTask;
import cn.dahe.dto.Result;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 定时任务管理Controller
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@RestController
@RequestMapping("/pro/schedule-task")
@Tag(name = "定时任务管理", description = "定时任务相关管理接口")
@ConditionalOnBean(DataPullScheduleTask.class)
public class ScheduleTaskController {

    @Resource
    private DataPullScheduleTask dataPullScheduleTask;

    @Operation(summary = "获取定时任务状态", description = "获取数据拉取定时任务的启用状态")
    @GetMapping("status")
    public Result<String> getTaskStatus(@CurrentUser LoginUserVO user) {
        log.info("用户{}查询定时任务状态", user.getUserId());
        
        String status = dataPullScheduleTask.getTaskStatus();
        return Result.ok(status);
    }

    @Operation(summary = "手动触发全部数据拉取", description = "手动触发所有类型的数据拉取任务")
    @PostMapping("manual-pull-all")
    public Result<String> manualPullAllData(@CurrentUser LoginUserVO user) {
        log.info("用户{}手动触发全部数据拉取", user.getUserId());
        
        try {
            dataPullScheduleTask.manualPullAllData(user);
            return Result.ok("手动触发全部数据拉取成功");
        } catch (Exception e) {
            log.error("手动触发全部数据拉取失败", e);
            return Result.error("手动触发失败：" + e.getMessage());
        }
    }

    @Operation(summary = "手动触发全站搜索数据拉取", description = "手动触发全站搜索数据拉取任务")
    @PostMapping("manual-pull-search")
    public Result<String> manualPullSearchData(@CurrentUser LoginUserVO user) {
        log.info("用户{}手动触发全站搜索数据拉取", user.getUserId());
        
        try {
            dataPullScheduleTask.pullAllSiteSearchData(user);
            return Result.ok("手动触发全站搜索数据拉取成功");
        } catch (Exception e) {
            log.error("手动触发全站搜索数据拉取失败", e);
            return Result.error("手动触发失败：" + e.getMessage());
        }
    }

    @Operation(summary = "手动触发附件检查数据拉取", description = "手动触发附件检查数据拉取任务")
    @PostMapping("manual-pull-attach")
    public Result<String> manualPullAttachData(@CurrentUser LoginUserVO user) {
        log.info("用户{}手动触发附件检查数据拉取", user.getUserId());
        
        try {
            dataPullScheduleTask.pullAttachUrlData(user);
            return Result.ok("手动触发附件检查数据拉取成功");
        } catch (Exception e) {
            log.error("手动触发附件检查数据拉取失败", e);
            return Result.error("手动触发失败：" + e.getMessage());
        }
    }

    @Operation(summary = "手动触发栏目更新数据拉取", description = "手动触发栏目更新检查数据拉取任务")
    @PostMapping("manual-pull-column")
    public Result<String> manualPullColumnData(@CurrentUser LoginUserVO user) {
        log.info("用户{}手动触发栏目更新数据拉取", user.getUserId());
        
        try {
            dataPullScheduleTask.pullSiteColumnData(user);
            return Result.ok("手动触发栏目更新数据拉取成功");
        } catch (Exception e) {
            log.error("手动触发栏目更新数据拉取失败", e);
            return Result.error("手动触发失败：" + e.getMessage());
        }
    }

    @Operation(summary = "手动触发首页更新数据拉取", description = "手动触发首页更新检查数据拉取任务")
    @PostMapping("manual-pull-index")
    public Result<String> manualPullIndexData(@CurrentUser LoginUserVO user) {
        log.info("用户{}手动触发首页更新数据拉取", user.getUserId());
        
        try {
            dataPullScheduleTask.pullSiteIndexData(user);
            return Result.ok("手动触发首页更新数据拉取成功");
        } catch (Exception e) {
            log.error("手动触发首页更新数据拉取失败", e);
            return Result.error("手动触发失败：" + e.getMessage());
        }
    }
}
