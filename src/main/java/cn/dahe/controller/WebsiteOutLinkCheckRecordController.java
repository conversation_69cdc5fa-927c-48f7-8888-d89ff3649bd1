package cn.dahe.controller;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.dto.WebsiteAccessOverviewDto;
import cn.dahe.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.WebsiteOutLinkCheckRecord;
import cn.dahe.query.WebsiteOutLinkCheckRecordQuery;
import cn.dahe.service.WebsiteOutLinkCheckRecordService;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-07-08
 */
@RestController
@RequestMapping("/pro/web/out-link")
@AllArgsConstructor
@Tag(name= "网站外链检查模块")
public class WebsiteOutLinkCheckRecordController {

    @Resource
    private WebsiteOutLinkCheckRecordService websiteOutLinkCheckRecordService;

    //分页-检查详情
    @PostMapping("page-check-record-detail")
    public Result<PageResult<WebsiteOutLinkCheckRecord>> pageCheckRecordDetail(WebsiteOutLinkCheckRecordQuery query, @CurrentUser LoginUserVO user) {
        return Result.ok(websiteOutLinkCheckRecordService.pageCheckRecordDetail(query));
    }

    /**
     * 外链统计 - 总体概览
     */
    @PostMapping("total-overview")
    public Result<WebsiteUpdateStatsDto> getDeadLinkOverview(WebsiteOutLinkCheckRecordQuery query,
                                                             @CurrentUser LoginUserVO user) {
        return Result.ok(websiteOutLinkCheckRecordService.totalOverview(query));
    }

    /**
     * 外链统计 - 网站分页统计详情
     */
    @PostMapping("/page-website-stats")
    public Result<PageResult<WebsiteAccessOverviewDto>> getWebsiteDeadLinkStatsPage(WebsiteOutLinkCheckRecordQuery query,
                                                                                    @CurrentUser LoginUserVO user) {
        return Result.ok(websiteOutLinkCheckRecordService.pageWebsiteStats(query));
    }

    /**
     * 过滤操作
     */
    @PostMapping("update-filter")
    public Result getDeadLinkOverview(@RequestParam(defaultValue = "0") int filter,
                                      @RequestParam(defaultValue = "0") int webId,
                                      @RequestParam(defaultValue = "0") String linkUrl,
                                      @CurrentUser LoginUserVO user) {
        boolean b = websiteOutLinkCheckRecordService.updateFilterByWebIdAndLinkUrl(filter, webId, linkUrl);
        return Result.ok();
    }


}
