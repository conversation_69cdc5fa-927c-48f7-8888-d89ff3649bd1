package cn.dahe.controller;

import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.dto.PageResult;
import cn.dahe.dto.Result;
import cn.dahe.dto.WebsiteAccessOverviewDto;
import cn.dahe.dto.WebsiteUpdateStatsDto;
import cn.dahe.entity.WebsiteAccessRecord;
import cn.dahe.query.WebsiteAccessRecordQuery;
import cn.dahe.service.WebsiteAccessRecordService;
import cn.dahe.vo.LoginUserVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-07-08
 */
@RestController
@RequestMapping("/pro/web/access-record")
@AllArgsConstructor
@Tag(name= "网站连通性检查模块")
public class WebsiteAccessRecordController {

    @Resource
    private WebsiteAccessRecordService websiteAccessRecordService;

    //列表-页面主页访问详细信息
    @PostMapping("page-home-access-detail")
    public Result<PageResult<WebsiteAccessRecord>> page(WebsiteAccessRecordQuery query, @CurrentUser LoginUserVO user) {
        return Result.ok(websiteAccessRecordService.page(query));
    }

    //列表-页面主页访问统计
    @PostMapping("page-home-access-stats")
    public Result<PageResult<WebsiteAccessOverviewDto>> pageStats(WebsiteAccessRecordQuery query, @CurrentUser LoginUserVO user) {
        return Result.ok(websiteAccessRecordService.pageStats(query));
    }

    //总体概览
    @PostMapping("home-access-total-stats")
    public Result<WebsiteUpdateStatsDto> totalStats(WebsiteAccessRecordQuery query, @CurrentUser LoginUserVO user) {
        return Result.ok(websiteAccessRecordService.totalStats(query));
    }


}
