package cn.dahe.controller.base;


import cn.dahe.entity.*;
import cn.dahe.service.*;
import cn.hutool.core.date.DateUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * 对外接口
 */
@RestController
@RequestMapping("/open-api")
public class OpenApiController {

    @Resource
    private ArticleService articleService;

    @Resource
    private ArticleContentService articleContentService;

    @Resource
    private ArticleCheckService articleCheckService;

    @Resource
    private ChannelService channelService;

    @Resource
    private WebsiteService webSiteService;

    @Resource
    private WebsiteAccessRecordService websiteAccessRecordService;

    public OpenApiController() {
    }

    @GetMapping
    @ResponseBody
    public String test() {
        Website byId = webSiteService.getById(7);

        for (int i = 0; i < 10; i++) {
            WebsiteAccessRecord record = new WebsiteAccessRecord();
            record.setWebId(byId.getId());
            record.setWebName(byId.getWebName());
            record.setWebUrl(byId.getWebUrl());

            record.setAccessTimeConsuming(50 + i * 30); // 访问耗时递增
            record.setHttpCode(i % 3 == 0 ? 500 : 200); // 每3条设置一个失败状态码
            // 设置检测时间为当前时间往前推 i 分钟
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MINUTE, -i);
            record.setCheckTime(calendar.getTime());

            // 设置创建时间为当前时间
            record.setCreateTime(new Date());

            record.setCreateTime(new Date());
            record.setSuccess(i % 3 == 0 ? 0 : 1); // 每3条为失败，其余为成功

            websiteAccessRecordService.save(record);
        }

        return "test11111111111111111111";
    }


    @GetMapping("test2")
    @ResponseBody
    public String test2() {
        Article article1 = new Article();
        article1.setId(1L);
        article1.setWebsiteId(101L);
        article1.setWebsiteName("河南政务网");
        article1.setTitle("关于优化营商环境的通知");
        article1.setWordCount(1234);
        article1.setCreateTime(DateUtil.parse("2025-07-15 10:00:00"));
        article1.setUpdateTime(DateUtil.parse("2025-07-15 10:30:00"));
        // article1.setCheckTime(DateUtil.parse("2025-07-15 10:10:00"));
        article1.setColumnId(12L);
        article1.setSource("省发改委");
        article1.setPubTime(DateUtil.parse("2025-07-14 16:00:00"));
        article1.setWriteTime(DateUtil.parse("2025-07-13 15:30:00"));
        article1.setLinkUrl("https://www.henan.gov.cn/news/notice1.html");
        article1.setSnapshot("<html>快照内容1...</html>");
        article1.setEditor("张三");
        article1.setAuditStatus(1);
        article1.setDisposalStatus(1);
        article1.setDisposalRemark("已与网站管理员沟通，要求7日内整改");
        article1.setRectifyStatus(1);


        Article article2 = new Article();
        article2.setId(2L);
        article2.setWebsiteId(102L);
        article2.setWebsiteName("郑州市人民政府网");
        article2.setTitle("2025年上半年经济形势分析报告");
        article2.setWordCount(3500);
        article2.setCreateTime(DateUtil.parse("2025-07-14 09:00:00"));
        article2.setUpdateTime(DateUtil.parse("2025-07-14 09:30:00"));
        // article2.setCheckTime(DateUtil.parse("2025-07-14 09:15:00"));
        article2.setColumnId(8L);
        article2.setSource("郑州统计局");
        article2.setPubTime(DateUtil.parse("2025-07-10 12:00:00"));
        article2.setWriteTime(DateUtil.parse("2025-07-09 18:00:00"));
        article2.setLinkUrl("https://www.zhengzhou.gov.cn/data/report2025.html");
        article2.setSnapshot("<html>快照内容2...</html>");
        article2.setEditor("李四");
        article2.setAuditStatus(2);
        article2.setDisposalStatus(2);
        article2.setDisposalRemark("内容涉嫌误导，已上报线索至纪检组");
        article2.setRectifyStatus(2);


        List<Article> articleList = Arrays.asList(article1, article2);

        articleService.saveBatch(articleList);


        ArticleCheck record1 = new ArticleCheck();
        record1.setId(1L);
        // record1.setTaskId(1001L);
        record1.setArticleId(2001L);
        record1.setErrorWord("政策性");
        record1.setSuggestWord("政策");
        // record1.setErrorType(1); // 比如 1 = 用词不规范
        // record1.setErrorLevel(1); // 一般
        record1.setPosition(56);
        record1.setCreateTime(DateUtil.parse("2025-07-15 10:00:00"));
        // record1.setCheckTime(DateUtil.parse("2025-07-15 10:05:00"));
        record1.setAuditStatus(0); // 未审核

        ArticleCheck record2 = new ArticleCheck();
        record2.setId(2L);
        // record2.setTaskId(1001L);
        record2.setArticleId(2001L);
        record2.setErrorWord("我们将会");
        record2.setSuggestWord("我们将");
        // record2.setErrorType(2); // 冗余类
        // record2.setErrorLevel(2); // 严重
        record2.setPosition(102);
        record2.setCreateTime(DateUtil.parse("2025-07-15 10:01:00"));
        // record2.setCheckTime(DateUtil.parse("2025-07-15 10:05:20"));
        record2.setAuditStatus(1); // 审核通过

        ArticleCheck record3 = new ArticleCheck();
        record3.setId(3L);
        // record3.setTaskId(1001L);
        record3.setArticleId(2001L);
        record3.setErrorWord("好象");
        record3.setSuggestWord("好像");
        // record3.setErrorType(3); // 错别字类
        // record3.setErrorLevel(3); // 疑似
        record3.setPosition(199);
        record3.setCreateTime(DateUtil.parse("2025-07-15 10:02:00"));
        // record3.setCheckTime(DateUtil.parse("2025-07-15 10:05:40"));
        record3.setAuditStatus(2); // 审核驳回


        List<ArticleCheck> list = Arrays.asList(record1, record2, record3);

        articleCheckService.saveBatch(list);


        List<ArticleContent> contentList = new ArrayList<>();

        // === 第一条 ===
        // ArticleContent content1 = new ArticleContent();
        // content1.setId(1L);
        // content1.setTaskId(1001L);
        // content1.setArticleId(2001L);
        // content1.setOriginalTitle("<h1>河南省政策性支持项目情况通报</h1>");
        // content1.setCleanTitle("河南省政策性支持项目情况通报");
        // content1.setMarkedTitle("河南省<span style='color:red;'>政策性</span>支持项目情况通报");
        // content1.setOriginalContent("<p>我们将会持续优化政务服务。</p><p>这些政策性支持...</p>");
        // content1.setCleanContent("我们将会持续优化政务服务。这些政策性支持...");
        // content1.setMarkedContent("我们<span style='color:red;'>将会</span>持续优化政务服务。这些<span style='color:red;'>政策性</span>支持...");
        // content1.setCreateTime(DateUtil.parse("2025-07-15 09:30:00"));
        // content1.setUpdateTime(DateUtil.parse("2025-07-15 09:45:00"));
        // contentList.add(content1);

        // === 第二条 ===
        // ArticleContent content2 = new ArticleContent();
        // content2.setId(2L);
        // content2.setTaskId(1001L);
        // content2.setArticleId(2002L);
        // content2.setOriginalTitle("<title>我们已经完成了阶段性目标</title>");
        // content2.setCleanTitle("我们已经完成了阶段性目标我们已经完成了阶段性目标我们已经完成了阶段性目标");
        // content2.setMarkedTitle("我们已经完成了<span style='color:red;'>阶段性</span>目标");
        // content2.setOriginalContent("<div>在市委市政府的领导下，我们已经完成了阶段性建设目标。</div>");
        // content2.setCleanContent("在市委市政府的领导下，我们已经完成了阶段性建设目标。");
        // content2.setMarkedContent("在市委市政府的领导下，我们已经完成了<span style='color:red;'>阶段性</span>建设目标。");
        // content2.setCreateTime(DateUtil.parse("2025-07-15 09:50:00"));
        // content2.setUpdateTime(DateUtil.parse("2025-07-15 10:05:00"));
        // contentList.add(content2);

        // === 第三条 ===
        // ArticleContent content3 = new ArticleContent();
        // content3.setId(3L);
        // content3.setTaskId(1002L);
        // content3.setArticleId(2003L);
        // content3.setOriginalTitle("<h2>本次演讲的是科技局李主任</h2>");
        // content3.setCleanTitle("本次演讲的是科技局李主任");
        // content3.setMarkedTitle("本次演讲的是<span style='color:red;'>科技局</span>李主任");
        // content3.setOriginalContent("<p>演讲内容涵盖了未来五年科技投入方向，科技局将在...</p>");
        // content3.setCleanContent("演讲内容涵盖了未来五年科技投入方向，科技局将在...");
        // content3.setMarkedContent("演讲内容涵盖了未来五年科技投入方向，<span style='color:red;'>科技局</span>将在...");
        // content3.setCreateTime(DateUtil.parse("2025-07-15 10:15:00"));
        // content3.setUpdateTime(DateUtil.parse("2025-07-15 10:20:00"));
        // contentList.add(content3);


        articleContentService.saveBatch(contentList);

        return "ok";


    }


}
