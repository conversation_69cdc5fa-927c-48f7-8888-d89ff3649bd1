package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文章检查内容实体类
 * 存储文章检查过程中标记错误后的内容
 * 与检查任务关联，每个任务对应一份标记内容
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@Accessors(chain = true)
@TableName("t_article_check_content")
public class ArticleCheckContent {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column("id")
    @ColumnComment("主键ID")
    private Long id;

    @Column("article_id")
    @ColumnComment("关联的文章ID")
    private Long articleId;

    @Column(name = "compressed_title", type = MySqlTypeConstant.TEXT)
    @ColumnComment("压缩的html标题")
    private String compressedTitle;

    @Column(name = "compressed_content", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("压缩的html内容")
    private String compressedContent;


    @Column(name = "cleaned_title", type = MySqlTypeConstant.TEXT)
    @ColumnComment("纯文本的标题")
    private String cleanedTitle;

    @Column(name = "cleaned_content", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("纯文本的内容")
    private String cleanedContent;

    @Column(name = "check_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("检查时间")
    @Schema(description = "检查时间", example = "2025-07-16 10:00:00")
    private Date checkTime;

    @Column(name = "check_status", comment = "检查状态 0未检查 1已检查 2检查失败待重试 3检查失败")
    @DefaultValue("0")
    @Schema(description = "检查状态 0未检查 1已检查 2检查失败待重试 3检查失败", example = "0")
    private Integer checkStatus;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;

    /**
     * 检查任务失败类型：1-本地异常，2-接口超时，3-接口失败
     */
    @Column(name = "fail_type", comment = "检查任务失败类型：1-本地异常，2-接口超时，3-接口失败")
    @Schema(description = "检查任务失败类型：1-本地异常，2-接口超时，3-接口失败", example = "1")
    private Integer failType;

    /**
     * 检查任务失败原因（包含失败详情、响应时间等信息）
     */
    @Column(name = "fail_reason", type = MySqlTypeConstant.TEXT)
    @ColumnComment("检查任务失败原因")
    @Schema(description = "检查任务失败原因", example = "调用校对服务超时，请求耗时：30000ms")
    private String failReason;
} 