package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 首页更新主表实体类
 * 数据组推送解析后的数据
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@TableName("chk_update_site_index")
@Schema(name = "首页更新主表", description = "网站首页更新检查信息")
public class ChkUpdateSiteIndex {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    @Schema(description = "编号", example = "1")
    private Long id;

    @Column(name = "group_id")
    @ColumnComment("分组ID")
    @Schema(description = "分组ID", example = "1")
    private Integer groupId;

    @Column("group_name")
    @ColumnComment("分组")
    @Schema(description = "分组", example = "网站分组")
    private String groupName;

    @Column(name = "website_id")
    @ColumnComment("网站ID")
    @Schema(description = "网站ID", example = "1")
    private Integer websiteId;

    @Column(name = "website_name")
    @ColumnComment("网站名称")
    @Schema(description = "网站名称", example = "网站名称")
    private String websiteName;

    @Column("website_index_url")
    @ColumnComment("网站首页地址")
    @Schema(description = "网站首页地址", example = "https://xxx.cn")
    private String websiteIndexUrl;

    @Column(name = "article_title", type = MySqlTypeConstant.TEXT)
    @ColumnComment("网站首页新闻内容标题")
    @Schema(description = "网站首页新闻内容标题", example = "网站首页新闻内容标题")
    private String articleTitle;

    @Column(name = "article_content", type = MySqlTypeConstant.LONGTEXT)
    @ColumnComment("网站首页新闻内容")
    @Schema(description = "网站首页新闻内容详情", example = "网站首页新闻内容详情")
    private String articleContent;

    @Column("article_url")
    @ColumnComment("文章地址")
    @Schema(description = "文章地址", example = "https://xxx.cn/article/123456")
    private String articleUrl;

    @Column(name = "article_publish_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("文章发布时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "文章发布时间", example = "2025-07-30 10:30:00")
    private Date articlePublishTime;

    @Column(name = "parse_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("解析时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "解析时间", example = "2025-07-30 10:30:00")
    private Date parseTime;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    private Date createTime;
}
