package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 附件表
 */
@Data
@TableName("t_attachment")
public class Attachment implements Serializable {
    private static final long serialVersionUID = -8878644562499614195L;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    @IsKey
    @IsAutoIncrement
    @Column(name = "id", comment = "id")
    private Integer id;

    /**
     * 附件名称
     */
    @Column(name = "name", comment = "附件名称，保存时名称")
    private String name;

    /**
     * 文件别名
     */
    @Column(name = "alias", comment = "附件别名，上传时名称")
    private String alias;

    /**
     * 文件后缀
     */
    @Column(name = "suffix", comment = "文件后缀")
    private String suffix;

    /**
     * 附件文件大小
     */
    @Column(name = "size", comment = "附件文件大小")
    @DefaultValue("0")
    private long size;

    /**
     * 附件url
     */
    @Column(name = "url", comment = "附件url")
    private String url;


    /**
     * 创建时间
     */
    @Column(name = "create_time", comment = "创建时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @Column(name = "privew_url", comment = "附件url")
    private String privewUrl;


}