package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 文章检查错误实体类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("t_article_check")
@Schema(name = "文章检查错误", description = "文章内容检查的错误信息记录")
public class ArticleCheck {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column("id")
    @ColumnComment("主键ID")
    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Column("website_id")
    @ColumnComment("关联的网站ID")
    @Schema(description = "关联的网站ID", example = "1")
    private Long websiteId;

    @Column("article_id")
    @ColumnComment("关联的文章ID")
    @Schema(description = "关联的文章ID", example = "1")
    private Long articleId;

    @Column("word_id")
    @ColumnComment("关联的错误词库ID")
    @Schema(description = "关联的错误词库ID", example = "1")
    private Long wordId;

    @Column("error_word")
    @ColumnComment("错误词")
    @Schema(description = "错误词", example = "中国人民共国")
    private String errorWord;

    @Column("suggest_word")
    @ColumnComment("建议正确用词")
    @Schema(description = "建议正确用词", example = "中华人民共和国")
    private String suggestWord;

    @Column("first_error_type_id")
    @ColumnComment("一级错误类型ID")
    @Schema(description = "一级错误类型ID", example = "1")
    private Long firstErrorTypeId;
    
    @Column("second_error_type_id")
    @ColumnComment("二级错误类型ID")
    @Schema(description = "二级错误类型ID", example = "2")
    private Long secondErrorTypeId;
    
    @Column("third_error_type_id")
    @ColumnComment("三级错误类型ID")
    @Schema(description = "三级错误类型ID", example = "3")
    private Long thirdErrorTypeId;

    @Column("error_level")
    @ColumnComment("错误等级：(严重1 一般2 疑似3 自定义词4 风险提示5)")
    @Schema( description = "错误等级 严重1 一般2 疑似3 自定义词4 风险提示5", example = "1")
    private Long errorLevel;
    
    @Column("position")
    @ColumnComment("在文本中的位置")
    @Schema(description = "错误位置 在纯文本中的字符位置", example = "100")
    private Integer position;

    @Column("html_error_word")
    @ColumnComment("html错误词")
    @Schema(description = "html错误词", example = "中国人民共国")
    private String htmlErrorWord;

    @Column("html_position")
    @ColumnComment("在HTML文本中的位置")
    @Schema(description = "错误位置 在HTML文本中的字符位置", example = "150")
    private Integer htmlPosition;

    @Column("article_location")
    @ColumnComment("错误位置：0正文 1标题")
    @DefaultValue("0")
    @Schema(description = "错误位置类型 0-正文 1-标题", example = "0")
    private Integer articleLocation;

    @Column(value = "context",type = MySqlTypeConstant.TEXT)
    @ColumnComment("错误上下文，纯文本句子，但长度有限制")
    @Schema(description = "错误上下文，纯文本句子，但长度有限制", example = "中国人民共国成立70周年")
    private String context;

    @Column(value = "marked_context",type = MySqlTypeConstant.TEXT)
    @ColumnComment("标记后的错误上下文")
    @Schema(description = "标记后的错误上下文", example = "<span class=\"sensitive_lv1_word\" data-check-id=\"1\">中国人民共国</span>成立70周年")
    private String markedContext;

    @Column(name = "audit_status")
    @ColumnComment("审核状态：0未审核 1审核通过 2审核驳回")
    @DefaultValue("0")
    @Schema( description = "审核状态 0-未审核 1-审核通过 2-审核驳回", example = "0")
    private Integer auditStatus;

    // @Column(name = "audit_user_id")
    // @ColumnComment("审核人ID")
    // @Schema(description = "审核人ID", example = "1")
    // private Long auditUserId;
    //
    // @Column(name = "audit_time", type = MySqlTypeConstant.DATETIME)
    // @ColumnComment("审核时间")
    // @Schema(description = "审核时间", example = "2025-07-16 10:00:00")
    // private Date auditTime;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @Schema(description = "创建时间", example = "2025-07-16 10:00:00")
    private Date createTime;

    @Column(name = "check_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    @Schema(description = "更新时间", example = "2025-07-16 10:00:00")
    private Date updateTime;
} 