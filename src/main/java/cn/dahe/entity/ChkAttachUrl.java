package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 附件检查实体类
 * 对应原型图功能需求
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@TableName("chk_attach_url")
@Schema(name = "附件检查", description = "网站附件检查信息")
public class ChkAttachUrl {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("编号")
    @Schema(description = "编号", example = "1")
    private Long id;

    @Column("website_id")
    @ColumnComment("网站ID")
    @Schema(description = "网站ID", example = "1")
    private Long websiteId;

    @Column("attach_name")
    @ColumnComment("附件名称")
    @Schema(description = "附件名称", example = "政策文件.pdf")
    private String attachName;

    @Column("attach_url")
    @ColumnComment("附件地址")
    @Schema(description = "附件地址", example = "https://www.example.com/files/policy.pdf")
    private String attachUrl;

    @Column("source_website")
    @ColumnComment("来源网站")
    @Schema(description = "来源网站", example = "河南省人民政府")
    private String sourceWebsite;

    @Column("source_page")
    @ColumnComment("来源页面")
    @Schema(description = "来源页面", example = "https://www.henan.gov.cn/policy/123")
    private String sourcePage;

    @Column(name = "publish_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("发布时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "发布时间", example = "2025-07-30 09:00:00")
    private Date publishTime;

    @Column(name = "detect_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("检测时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "检测时间", example = "2025-07-30 10:30:00")
    private Date detectTime;

    @Column("check_status")
    @ColumnComment("检测状态")
    @DefaultValue("0")
    @Schema(description = "检测状态(1:正常,2:失效,0:未检查)", example = "1")
    private Integer checkStatus;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    private Date createTime;

    @Column("create_by")
    @ColumnComment("创建人")
    @Schema(description = "创建人", example = "1")
    private Long createBy;

    @Column("update_by")
    @ColumnComment("更新人")
    @Schema(description = "更新人", example = "1")
    private Long updateBy;

    @Column("is_del")
    @ColumnComment("是否删除")
    @DefaultValue("0")
    @Schema(description = "是否删除(0:否,1:是)", example = "0")
    private Integer isDel;
}
