package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 网站实体类
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@TableName("t_website")
public class Website {

    @IsKey
    @IsAutoIncrement
    @Column("id")
    @ColumnComment("ID")
    private Integer id;

    @Column(value = "web_name")
    @ColumnComment("网站名称")
    private String webName;

    @Column("web_url")
    @ColumnComment("网站地址")
    private String webUrl;

    @Column("web_code_id")
    @ColumnComment("网站标识码")
    private String webCodeId;

    @Column(name = "create_time", comment = "添加时间", type = MySqlTypeConstant.DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Column(value = "create_user_id", defaultValue = "0")
    @ColumnComment("创建用户")
    private Integer createUserId;

    @Column(value = "create_user_name")
    @ColumnComment("创建用户")
    private String createUserName;

    /**
     * 状态 -1：删除 0：停用 1：启用
     */
    @Column(name = "status", comment = "状态 0：停用 1：启用",type = MySqlTypeConstant.TINYINT)
    @DefaultValue("1")
    private int status;

    @Column(name = "group_id", comment = "分组ID")
    private Integer groupId;

    @Column(name = "check_strategy", comment = "检查策略：(查全/查准)或者其他",type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Integer checkStrategy;

    @TableLogic
    @Column(name = "is_del", comment = "是否删除 0：未删除 1：已删除",type = MySqlTypeConstant.TINYINT)
    @DefaultValue("0")
    private Boolean isDel;

    /**
     * 数据组网站ID
     */
    @Column(name = "sjz_web_id", comment = "数据组网站id", defaultValue = "0")
    private Integer sjzWebId;


}
