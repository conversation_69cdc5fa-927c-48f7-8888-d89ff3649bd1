package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 采集文章实体类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("t_article")
@Schema(name = "文章信息", description = "采集的文章基本信息")
public class Article {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column("id")
    @ColumnComment("主键ID")
    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Column("website_id")
    @ColumnComment("关联的网站ID")
    @Schema(description = "网站ID", example = "1")
    private Long websiteId;

    @Column(value = "website_name")
    @ColumnComment("网站名称")
    @Schema(description = "网站名称", example = "大河网")
    private String websiteName;

    @Column("title")
    @ColumnComment("文章标题")
    @Schema(description = "文章标题", example = "关于加强网络安全管理的通知")
    private String title;

    @Column("word_count")
    @ColumnComment("文章字数")
    @DefaultValue("0")
    @Schema(description = "文章字数", example = "1000")
    private Integer wordCount;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @Schema(description = "创建时间", example = "2025-07-16 10:00:00")
    private Date createTime;

    @Column(name = "acquisition_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("采集时间")
    @Schema(description = "采集时间", example = "2025-07-16 10:00:00")
    private Date acquisitionTime;

    @Column(name = "check_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("检测时间")
    @Schema(description = "检测时间", example = "2025-07-16 10:00:00")
    private Date checkTime;

    @Column(name = "check_content_id")
    @ColumnComment("检测正文id")
    @Schema(description = "检测正文id")
    private Long checkContentId;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    @Schema(description = "更新时间", example = "2025-07-16 10:00:00")
    private Date updateTime;

    @Column("column_id")
    @ColumnComment("栏目ID")
    @Schema(description = "栏目ID", example = "1")
    private Long columnId;

    @Column("source")
    @ColumnComment("来源")
    @Schema(description = "来源", example = "大河网")
    private String source;

    /**
     * 文章的发布时间
     */
    @Column(name = "pub_time", comment = "文章的发布时间", type = MySqlTypeConstant.DATETIME)
    @Schema(description = "发布时间", example = "2025-07-16 10:00:00")
    private Date pubTime;

    /**
     * 文章的成文时间
     */
    @Column(name = "write_time", comment = "文章的成文时间", type = MySqlTypeConstant.DATETIME)
    @Schema(description = "成文时间", example = "2025-07-16 10:00:00")
    private Date writeTime;


    /**
     * 文章访问链接
     */
    @Column(name = "link_url", type = MySqlTypeConstant.TEXT, comment = "访问链接")
    @Schema(description = "访问链接", example = "https://www.dahe.cn/article/123456")
    private String linkUrl;


    @Column(value = "snapshot", type = MySqlTypeConstant.TEXT, comment = "快照")
    @ColumnComment("快照")
    @Schema(description = "快照", example = "文章内容的快照...")
    private String snapshot;


    /**
     * 作者
     */
    @Column(name = "editor", type = MySqlTypeConstant.TEXT, comment = "访问链接")
    @Schema(description = "作者", example = "张三")
    private String editor;

    /**
     * 审核状态
     */
    @Column(name = "audit_status", comment = "审核状态 0未审核 1审核通过 2审核驳回")
    @DefaultValue("0")
    @Schema(description = "审核状态 0-未审核 1-审核通过 2-审核驳回", example = "0")
    private Integer auditStatus;

    @Column(name = "audit_user_id")
    @ColumnComment("审核人ID")
    @Schema(description = "审核人ID", example = "1")
    private Long auditUserId;

    @Column(name = "audit_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("审核时间")
    @Schema(description = "审核时间", example = "2025-07-16 10:00:00")
    private Date auditTime;

    /**
     * 处置状态
     */
    @Column(name = "disposal_status", comment = "处置状态 0未处置 1已约谈整改 2已上报线索 3已关停站点")
    @DefaultValue("0")
    @Schema(description = "处置状态 0-未处置 1-已约谈整改 2-已上报线索 3-已关停站点", example = "0")
    private int disposalStatus;

    /**
     * 处置备注（最多200字）
     */
    @Column(name = "disposal_remark", comment = "处置备注", length = 200)
    @Schema(description = "处置备注 最多200字", example = "已与网站负责人沟通，要求立即整改")
    private String disposalRemark;

    /**
     * 整改状态（0未整改，1整改中，2已整改，3无需整改）
     */
    @Column(name = "rectify_status", comment = "整改状态 0未下发 1待整改 2已整改 3无需整改")
    @DefaultValue("0")
    @Schema(description = "整改状态 0-未下发 1-待整改 2-已整改 3-无需整改", example = "0")
    private int rectifyStatus;

}