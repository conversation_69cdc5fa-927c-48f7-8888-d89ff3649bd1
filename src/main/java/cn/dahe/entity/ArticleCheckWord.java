package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 文章检查错误词库实体类
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@TableName("t_article_check_word")
@Schema(name = "文章检查错误词库实体类", description = "文章内容检查的错词唯一信息")
public class ArticleCheckWord {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column("id")
    @ColumnComment("主键ID")
    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Column("error_word")
    @ColumnComment("错误词")
    @Schema(description = "错误词", example = "中国人民共国")
    private String errorWord;

    @Column("suggest_word")
    @ColumnComment("建议正确用词")
    @Schema(description = "建议正确用词", example = "中华人民共和国")
    private String suggestWord;

    @Column("first_error_type")
    @ColumnComment("一级错误类型ID")
    @Schema(description = "一级错误类型ID", example = "1")
    private Long firstErrorType;

    @Column("second_error_type")
    @ColumnComment("二级错误类型ID")
    @Schema(description = "二级错误类型ID", example = "2")
    private Long secondErrorType;

    @Column("third_error_type")
    @ColumnComment("三级错误类型ID")
    @Schema(description = "三级错误类型ID", example = "3")
    private Long thirdErrorType;

    @Column("error_level")
    @ColumnComment("错误等级：(严重1 一般2 疑似3 自定义词4 风险提示5)")
    @Schema( description = "错误等级 严重1 一般2 疑似3 自定义词4 风险提示5", example = "1")
    private Long errorLevel;

    @Column(value = "is_filtered",type = MySqlTypeConstant.TINYINT)
    @ColumnComment("是否过滤（0-不过滤，1-过滤）")
    @DefaultValue("0")
    @Schema(description = "是否过滤 0-不过滤 1-过滤", example = "0")
    private Boolean isFiltered;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    @Schema(description = "创建时间", example = "2025-07-28 10:00:00")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    @Schema(description = "更新时间", example = "2025-07-28 10:00:00")
    private Date updateTime;
} 