package cn.dahe.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;

import java.util.Date;

/**
 * 内容检查任务实体类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("t_check_task")
public class CheckTask {

    @IsKey
    @IsAutoIncrement
    @TableId
    @Column("id")
    @ColumnComment("主键ID")
    private Long id;

    @Column(value = "check_strategy",type = MySqlTypeConstant.TINYINT)
    @ColumnComment("检查策略：(查全/查准)或者其他")
    private Integer checkStrategy;

    @Column("website_id")
    @ColumnComment("关联的网站ID")
    private Integer websiteId;

    @Column("task_status")
    @ColumnComment("任务状态：0-待执行 1-执行中 2-已完成 3-执行失败")
    @DefaultValue("0")
    private Integer taskStatus;


    @Column(name = "start_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("开始执行时间")
    private Date startTime;

    @Column(name = "end_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("结束时间")
    private Date endTime;

    @Column("fail_reason")
    @ColumnComment("失败原因")
    private String failReason;

    @Column(name = "create_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("创建时间")
    @DefaultValue("CURRENT_TIMESTAMP")
    private Date createTime;

    @Column(name = "update_time", type = MySqlTypeConstant.DATETIME)
    @ColumnComment("更新时间")
    @IgnoreUpdate
    @DefaultValue("NULL ON UPDATE CURRENT_TIMESTAMP")
    private Date updateTime;
}
