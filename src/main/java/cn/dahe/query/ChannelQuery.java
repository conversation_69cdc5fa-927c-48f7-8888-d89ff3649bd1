package cn.dahe.query;

import cn.dahe.dto.Query;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 网站信息查询
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ChannelQuery extends Query {

    private String beginTime = "";

    private String endTime = "";

    private String name = "";

    private String url = "";

    private String enable = "";

    //网站id
    private String webSiteId = "";




}