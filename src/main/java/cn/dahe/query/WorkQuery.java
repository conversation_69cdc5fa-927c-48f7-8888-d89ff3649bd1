package cn.dahe.query;

import cn.dahe.dto.Query;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WorkQuery extends Query {

    /**
     * 题目
     */
    private String userId;

    /**
     * 题目
     */
    private String title;

    /**
     * 项目
     */
    private String dictIdProject;

    /**
     * 体裁
     */
    private String dictIdGenre;

    /**
     * 状态
     */
    private String status;

    /**
     * 部门名称
     */
    private String depName;

    private String beginTime = "";

    private String endTime = "";

    private String ids = "";

    // 初评 定评
    private String reviewType = "";

    //状态
    private String reviewStatus = "";

    private String awardLevelId = "";

    //作评编号
    private String workNum = "";

    //定评编号
    private String workNumFinal = "";

    //组别
    private String groupType="";


    private String adminFirstAwardLevelId;


    private String adminFinalAwardLevelId;


    private List<Integer> WorkAdminReviewStatusList = new ArrayList<>();


}