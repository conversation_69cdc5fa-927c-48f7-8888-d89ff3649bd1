package cn.dahe.query;

import cn.dahe.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 首页更新主表查询参数
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "首页更新主表查询参数")
public class ChkUpdateSiteIndexQuery extends Query {

    @Schema(description = "分组名称", example = "政府网站")
    private String groupName = "";

    @Schema(description = "网站名称", example = "河南省人民政府")
    private String websiteName = "";

    @Schema(description = "网站首页地址", example = "https://www.henan.gov.cn")
    private String websiteIndexUrl = "";

    @Schema(description = "解析开始时间", example = "2025-07-01 00:00:00")
    private String parseBeginTime = "";

    @Schema(description = "解析结束时间", example = "2025-07-31 23:59:59")
    private String parseEndTime = "";

    @Schema(description = "创建开始时间", example = "2025-07-01 00:00:00")
    private String createBeginTime = "";

    @Schema(description = "创建结束时间", example = "2025-07-31 23:59:59")
    private String createEndTime = "";
}
