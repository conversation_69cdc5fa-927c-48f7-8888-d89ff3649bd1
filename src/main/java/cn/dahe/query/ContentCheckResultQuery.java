package cn.dahe.query;

import cn.dahe.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 内容检查结果查询参数
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "内容检查结果查询参数")
public class ContentCheckResultQuery extends Query {

    @Schema(description = "内容标题", example = "关于加强网站建设的通知")
    private String contentTitle = "";

    @Schema(description = "来源网站", example = "河南省人民政府")
    private String sourceWebsite = "";

    @Schema(description = "发布开始时间", example = "2025-07-01 00:00:00")
    private String publishStartTime = "";

    @Schema(description = "发布结束时间", example = "2025-07-31 23:59:59")
    private String publishEndTime = "";

    @Schema(description = "检测开始时间", example = "2025-07-01 00:00:00")
    private String checkStartTime = "";

    @Schema(description = "检测结束时间", example = "2025-07-31 23:59:59")
    private String checkEndTime = "";

    @Schema(description = "检测结果", example = "发现问题")
    private String checkResult = "";

    @Schema(description = "问题类型", example = "错别字")
    private String issueType = "";

    @Schema(description = "严重程度", example = "一般")
    private String severity = "";

    @Schema(description = "处理状态", example = "待处理")
    private String processStatus = "";

    @Schema(description = "处理人", example = "张三")
    private String processor = "";

    @Schema(description = "排序字段", example = "checkTime")
    private String sortField = "checkTime";

    @Schema(description = "排序方向", example = "desc")
    private String sortDirection = "desc";
}
