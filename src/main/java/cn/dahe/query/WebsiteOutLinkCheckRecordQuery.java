package cn.dahe.query;

import cn.dahe.dto.Query;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WebsiteOutLinkCheckRecordQuery extends Query {
    // 分组类型
    private String groupType = "";

    private String webId = "";

    private String beginTime = "";

    private String endTime = "";

    private String latest = "";

    private String filter = "";
    /**
     * 死链类型
     */
    private String deadLinkType = "";

    /**
     * 死链地址（模糊搜索字段）
     */
    private String deadLinkUrlLike = "";

    /**
     * 死链地址（精确搜索字段）
     */
    private String deadLinkUrl = "";

    /**
     * 死链状态码
     */
    private String httpCode = "";


    /**
     * 记录Id
     */
    private String recordId = "";

    /**
     * 来源页面
     */
    private String sourcePage="";
}
