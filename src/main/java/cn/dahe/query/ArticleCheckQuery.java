package cn.dahe.query;

import cn.dahe.enums.ArticleSortTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 文章检查查询参数
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@Schema(description = "文章检查查询参数")
public class ArticleCheckQuery {

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小值为 1")
    @Schema(description = "页码", example = "1", type = "integer",requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer page = 1;

    @NotNull(message = "每页条数不能为空")
    @Min(value = 1, message = "每页条数最小值为 1")
    @Schema(description = "每页大小", example = "10", type = "integer")
    private Integer limit = 10;

    @Schema(description = "一级错误类型", example = "1,2,3", type = "array", implementation = Long.class)
    private List<Long> firstErrorTypes;

    @Schema(description = "二级错误类型", type = "array", implementation = Long.class)
    private List<Long> secondErrorTypes;

    @Schema(description = "三级错误类型", type = "array", implementation = Long.class)
    private List<Long> thirdErrorTypes;

    @Schema(description = "过滤状态", type = "array", implementation = Integer.class)
    private List<Integer> filterStatuses;

    @Schema(description = "是否只显示指定的错误类型（true: 只显示指定类型的错误列表，false: 显示文章的所有错误列表）", type = "boolean", defaultValue = "false")
    private Boolean onlyShowSpecifiedErrors = false;

    @Schema(description = "关键词，匹配文章标题", type = "string")
    private String keyword;

    @Schema(description = "错误等级", example = "1,2,3,4,5", type = "array", implementation = Integer.class)
    private List<Integer> errorLevels;

    @Schema(description = "文章审核状态", example = "0,1,2", type = "array", implementation = Integer.class)
    private List<Integer> articleAuditStatuses;

    @Schema(description = "错误审核状态", example = "0,1,2", type = "array", implementation = Integer.class)
    private List<Integer> errorAuditStatuses;

    @Schema(description = "网站ID列表", example = "6,7", type = "array", implementation = Long.class)
    private List<Long> websiteIds;

    @Schema(description = "发布时间起始时间", type = "string", format = "date-time")
    private Date pubStartTime;

    @Schema(description = "发布时间结束时间", type = "string", format = "date-time")
    private Date pubEndTime;

    @Schema(description = "检测时间起始时间", type = "string", format = "date-time")
    private Date checkStartTime;

    @Schema(description = "检测时间结束时间", type = "string", format = "date-time")
    private Date checkEndTime;

    @Schema(description = "排序类型（1:发布时间降序 2:发布时间升序 3:检测时间降序 4:检测时间升序）", example = "1", type = "integer")
    private Integer sortType = 1;



    public ArticleSortTypeEnum getSortTypeEnum() {
        return ArticleSortTypeEnum.getByCode(sortType);
    }
} 