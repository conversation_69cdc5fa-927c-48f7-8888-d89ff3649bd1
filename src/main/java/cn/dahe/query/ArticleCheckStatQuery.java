package cn.dahe.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-05
 */
@Data
public class ArticleCheckStatQuery {

    Integer page = 1;

    Integer limit = 10;

    @Schema(description = "网站ID列表", example = "6,7", type = "array", implementation = Long.class)
    private List<Long> websiteIds;

    @Schema(description = "错误等级", example = "1,2,3,4,5", type = "array", implementation = Integer.class)
    private List<Integer> errorLevels;

    /**
     * 发布开始时间
     */
    @Schema(description = "发布开始时间", example = "2025-07-16")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date pubBeginDate;
    /**
     * 发布结束时间
     */
    @Schema(description = "发布结束时间", example = "2025-07-17")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date pubEndDate;


}
