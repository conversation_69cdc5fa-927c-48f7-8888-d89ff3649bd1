package cn.dahe.query;

import cn.dahe.dto.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 附件检查查询参数 - 对应原型图功能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "附件检查查询参数")
public class ChkAttachUrlQuery extends Query {

    // ==================== 原型图对应字段 ====================
    @Schema(description = "站点类型：1网站，2分组", example = "站点类型：1网站，2分组")
    private int siteType = 1;

    @Schema(description = "站点值", example = "站点值")
    private String siteValue = "";

    @Schema(description = "排序时间类型：1发布时间，2检测时间", example = "排序时间类型：1发布时间，2检测时间")
    private int timeType = 2;

    @Schema(description = "发布开始时间", example = "排序值：asc升序，desc降序")
    private String orderValue = "desc";

    @Schema(description = "排序开始时间", example = "排序开始时间")
    private String startTime = "";

    @Schema(description = "排序结束时间")
    private String endTime = "";

}
