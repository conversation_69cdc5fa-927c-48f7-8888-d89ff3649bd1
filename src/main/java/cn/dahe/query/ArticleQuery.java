package cn.dahe.query;

import cn.dahe.dto.Query;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ArticleQuery extends Query {
    // 分组类型
    private String groupType = "";
    //网站
    private String webId = "";
    //检查开始时间
    private String beginTime = "";
    //检查结束时间
    private String endTime = "";
    //文章发布开始时间
    private String pubBeginTime = "";
    //文章发布结束时间
    private String pubEndTime = "";
    //转载信源名称
    private String source = "";
    //文章审核结果
    private String auditStatus = "";
    //处置状态
    private String disposalStatus = "";
    //整改状态
    private String rectifyStatus = "";
    //错误词
    private String errorWord = "";
    //建议词
    private String suggestWord = "";
    //错误类型
    private String errorLevel = "";
    //二级错误类型
    private String errorType = "";

    //内容检查审核结果
    private String errorAuditStatus = "";


}
