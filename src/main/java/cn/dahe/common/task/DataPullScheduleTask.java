package cn.dahe.common.task;

import cn.dahe.dto.Result;
import cn.dahe.service.ChkAllSiteSearchService;
import cn.dahe.service.ChkAttachUrlService;
import cn.dahe.service.ChkUpdateSiteColumnService;
import cn.dahe.service.ChkUpdateSiteIndexService;
import cn.dahe.vo.LoginUserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 数据拉取定时任务
 * 定时从采集中心拉取各类检查数据
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "schedule.data-pull.enabled", havingValue = "true", matchIfMissing = false)
public class DataPullScheduleTask {

    @Resource
    private ChkAllSiteSearchService chkAllSiteSearchService;

    @Value("${schedule.data-pull.all-site-search.enabled:true}")
    private boolean allSiteSearchEnabled;

    @Value("${schedule.data-pull.attach-url.enabled:true}")
    private boolean attachUrlEnabled;

    @Value("${schedule.data-pull.site-column.enabled:true}")
    private boolean siteColumnEnabled;

    @Value("${schedule.data-pull.site-index.enabled:true}")
    private boolean siteIndexEnabled;

    /**
     * 拉取全站搜索数据
     * 每小时执行一次
     */
    @Scheduled(cron = "${schedule.data-pull.all-site-search.cron:0 0 * * * ?}")
    public void pullAllSiteSearchData(LoginUserVO user) {
        if (!allSiteSearchEnabled) {
            log.debug("全站搜索数据拉取已禁用");
            return;
        }

        log.info("开始执行全站搜索数据拉取定时任务");
        try {
            Result<String> result = chkAllSiteSearchService.pullDataFromCollectionCenter(user);
            if (result.getCode() == 200) {
                log.info("全站搜索数据拉取定时任务执行成功: {}", result.getData());
            } else {
                log.error("全站搜索数据拉取定时任务执行失败: {}", result.getMsg());
            }
        } catch (Exception e) {
            log.error("全站搜索数据拉取定时任务执行失败", e);
        }
    }

    /**
     * 拉取附件检查数据
     * 每2小时执行一次
     */
    @Scheduled(cron = "${schedule.data-pull.attach-url.cron:0 0 */2 * * ?}")
    public void pullAttachUrlData(LoginUserVO user) {
        if (!attachUrlEnabled) {
            log.debug("附件检查数据拉取已禁用");
            return;
        }

        log.info("开始执行附件检查数据拉取定时任务");
        try {
            // TODO: 调用Service层的拉取方法
            // chkAttachUrlService.pullDataFromCollectionCenter(systemUser);
            log.info("附件检查数据拉取定时任务执行成功");
        } catch (Exception e) {
            log.error("附件检查数据拉取定时任务执行失败", e);
        }
    }

    /**
     * 拉取栏目更新检查数据
     * 每3小时执行一次
     */
    @Scheduled(cron = "${schedule.data-pull.site-column.cron:0 0 */3 * * ?}")
    public void pullSiteColumnData(LoginUserVO user) {
        if (!siteColumnEnabled) {
            log.debug("栏目更新检查数据拉取已禁用");
            return;
        }

        log.info("开始执行栏目更新检查数据拉取定时任务");
        try {
            // TODO: 调用Service层的拉取方法
            // chkUpdateSiteColumnService.pullDataFromCollectionCenter(systemUser);
            log.info("栏目更新检查数据拉取定时任务执行成功");
        } catch (Exception e) {
            log.error("栏目更新检查数据拉取定时任务执行失败", e);
        }
    }

    /**
     * 拉取首页更新检查数据
     * 每4小时执行一次
     */
    @Scheduled(cron = "${schedule.data-pull.site-index.cron:0 0 */4 * * ?}")
    public void pullSiteIndexData(LoginUserVO user) {
        if (!siteIndexEnabled) {
            log.debug("首页更新检查数据拉取已禁用");
            return;
        }

        log.info("开始执行首页更新检查数据拉取定时任务");
        try {
            // TODO: 调用Service层的拉取方法
            // chkUpdateSiteIndexService.pullDataFromCollectionCenter(systemUser);
            log.info("首页更新检查数据拉取定时任务执行成功");
        } catch (Exception e) {
            log.error("首页更新检查数据拉取定时任务执行失败", e);
        }
    }

    /**
     * 综合数据拉取任务
     * 每天凌晨2点执行一次，拉取所有类型的数据
     */
    @Scheduled(cron = "${schedule.data-pull.comprehensive.cron:0 0 2 * * ?}")
    public void pullAllData(LoginUserVO user) {
        log.info("开始执行综合数据拉取定时任务");
        
        try {
            // 按顺序拉取各类数据
            if (allSiteSearchEnabled) {
                log.info("拉取全站搜索数据...");
                pullAllSiteSearchData(user);
                Thread.sleep(5000); // 间隔5秒
            }

            if (attachUrlEnabled) {
                log.info("拉取附件检查数据...");
                pullAttachUrlData(user);
                Thread.sleep(5000); // 间隔5秒
            }

            if (siteColumnEnabled) {
                log.info("拉取栏目更新检查数据...");
                pullSiteColumnData(user);
                Thread.sleep(5000); // 间隔5秒
            }

            if (siteIndexEnabled) {
                log.info("拉取首页更新检查数据...");
                pullSiteIndexData(user);
            }

            log.info("综合数据拉取定时任务执行完成");
        } catch (Exception e) {
            log.error("综合数据拉取定时任务执行失败", e);
        }
    }

    /**
     * 手动触发全部数据拉取
     * 提供给管理员手动调用
     */
    public void manualPullAllData(LoginUserVO user) {
        log.info("手动触发全部数据拉取");
        pullAllData(user);
    }

    /**
     * 获取定时任务状态
     */
    public String getTaskStatus() {
        StringBuilder status = new StringBuilder();
        status.append("数据拉取定时任务状态:\n");
        status.append("- 全站搜索数据拉取: ").append(allSiteSearchEnabled ? "启用" : "禁用").append("\n");
        status.append("- 附件检查数据拉取: ").append(attachUrlEnabled ? "启用" : "禁用").append("\n");
        status.append("- 栏目更新检查数据拉取: ").append(siteColumnEnabled ? "启用" : "禁用").append("\n");
        status.append("- 首页更新检查数据拉取: ").append(siteIndexEnabled ? "启用" : "禁用").append("\n");
        return status.toString();
    }
}
