package cn.dahe.common.config.cas;

import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.common.constants.CacheConstants;
import cn.dahe.common.constants.LoginConstants;
import cn.dahe.common.constants.SecurityConstants;
import cn.dahe.common.constants.StatusConstants;
import cn.dahe.common.context.SecurityContextHolder;
import cn.dahe.common.model.ResultCode;
import cn.dahe.dto.Result;
import cn.dahe.entity.User;
import cn.dahe.redis.RedisService;
import cn.dahe.service.UserService;
import cn.dahe.utils.JwtUtils;
import cn.dahe.utils.SpringUtils;
import cn.dahe.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.auth0.jwt.JWT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.concurrent.TimeUnit;

public class LoginFilter implements Filter {

    Logger logger = LoggerFactory.getLogger(LoginFilter.class);

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request_ = (HttpServletRequest) request;
        HttpServletResponse response_ = (HttpServletResponse) response;

        String requestURI = request_.getRequestURI();
        logger.info("LoginFilter执行，请求URL: {}", requestURI);

        // 检查是否是API认证接口，如果是则跳过系统登录认证
        if (isApiAuthUrl(requestURI)) {
            logger.info("API认证接口，跳过系统登录认证: {}", requestURI);
            chain.doFilter(request, response);
            return;
        }

        String errorMsg = checkToken();
        if (StringUtils.isNotBlank(errorMsg)) {
            logger.warn("Token验证失败: {}", errorMsg);
            response_.setHeader("content-type", "application/json");
            response_.setCharacterEncoding("UTF-8");
            PrintWriter out = response_.getWriter();
            String res = JSON.toJSONString(Result.error(ResultCode.NoLoginError, errorMsg));
            out.write(res);
            return;
        }

        logger.info("Token验证成功，用户信息已设置到SecurityContextHolder");
        chain.doFilter(request, response);
        // 销毁上下文的存放
        SecurityContextHolder.remove();
    }

    private String checkToken() {
        String token = SecurityUtils.getToken();
        if (StringUtils.isBlank(token)) {
            return LoginConstants.LOGIN_FAIL_TOKEN_MISSING;
        }
        Integer userId = 0;
        try {
            JwtUtils.verifyToken(token);
            userId = Integer.parseInt(JWT.decode(token).getAudience().get(0));
        } catch (Exception e) {
            return LoginConstants.LOGIN_FAIL_TOKEN_PARSE_ERROR;
        }
        //是否已经禁用
        UserService userService = SpringUtils.getBean(UserService.class);
        User user = userService.getById(userId);
        if (user == null || user.getStatus() != StatusConstants.COMMON_NORMAL) {
            return LoginConstants.LOGIN_FAIL_USER_NOT_FOUND_OR_DISABLED;
        }

        RedisService redisService = SpringUtils.getBean(RedisService.class);
        String tokenKey = CacheConstants.LOGIN_TOKEN_KEY + userId;
        String userKey = CacheConstants.LOGIN_USER_KEY + userId;
        String cacheToken = redisService.getCacheObject(tokenKey);
        if (StringUtils.isBlank(cacheToken) || !cacheToken.equals(token)) {
            return LoginConstants.LOGIN_FAIL_CACHE_TOKEN_MISMATCH;
        }
        if (!redisService.hasKey(userKey)) {
            return LoginConstants.LOGIN_FAIL_USER_NOT_IN_CACHE;
        }
        //续期用户信息
        redisService.expire(tokenKey, CacheConstants.EXPIRATION, TimeUnit.MINUTES);
        redisService.expire(userKey, CacheConstants.EXPIRATION, TimeUnit.MINUTES);
        //存放当前登录人的相关信息
        SecurityContextHolder.set(SecurityConstants.LOGIN_USER, redisService.getCacheObject(userKey));
        return "";
    }

    /**
     * 检查是否是API认证接口
     */
    private boolean isApiAuthUrl(String requestURI) {
        // API认证接口列表
        String[] apiAuthUrls = {
            "/pro/chk-all-site-search/receive-push-data",
            "/pro/chk-attach-url/receive-push-data",
            "/pro/chk-update-site-column/receive-push-data",
            "/pro/chk-update-site-index/receive-push-data"
        };

        for (String apiUrl : apiAuthUrls) {
            if (requestURI.equals(apiUrl)) {
                return true;
            }
        }
        return false;
    }
}

