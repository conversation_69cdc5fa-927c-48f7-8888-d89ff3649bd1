package cn.dahe.common.annotation.impl;

import cn.dahe.common.advice.NotLoginException;
import cn.dahe.common.auth.SecurityUtils;
import cn.dahe.common.annotation.CurrentUser;
import cn.dahe.vo.LoginUserVO;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;


@Component("userParamResolver")
public class CurrentUserParamResolver implements HandlerMethodArgumentResolver {


    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.hasParameterAnnotation(CurrentUser.class);
    }

    @Override
    public LoginUserVO resolveArgument(MethodParameter methodParameter,
                                       ModelAndViewContainer modelAndViewContainer,
                                       NativeWebRequest nativeWebRequest,
                                       WebDataBinderFactory webDataBinderFactory) {
        LoginUserVO loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            throw new NotLoginException("当前用户未登录");
        }
        // 续费
        return loginUser;
    }
}