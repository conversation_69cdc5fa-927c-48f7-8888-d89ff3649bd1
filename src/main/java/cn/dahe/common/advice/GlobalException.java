package cn.dahe.common.advice;


import cn.dahe.common.model.ResultCode;
import cn.dahe.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;


/**
 * 捕获全局异常
 */
@Slf4j
@RestControllerAdvice
public class GlobalException {

//    @Resource
//    private CasCustomerConfig casCustomerConfig;

    /**
     * 未知异常捕获
     *
     * @param e 异常
     * @return 结果
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Result<String> handleException(Exception e) {
        log.info("GlobalException-->Exception【{}】", e.getMessage());
        e.printStackTrace();
        return Result.error(ResultCode.INTERNAL_SERVER_ERROR);
    }


    @ExceptionHandler(BadSqlGrammarException.class)
    public Result<String> handleBadSqlGrammarException(BadSqlGrammarException e) {
        log.info("GlobalException-->BadSqlGrammarException报错信息【{}】", e.getMessage());
        e.printStackTrace();
        return Result.error(ResultCode.SystemError);
    }

    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(NotPermissionException.class)
    @ResponseBody
    public Result<String> handleNotPermissionException(NotPermissionException e) {
        log.info("GlobalException-->NotPermissionException【{}】", e.getMessage());
        e.printStackTrace();
        return Result.error(ResultCode.NotGrant, e.getMessage());
    }

    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(NotRoleException.class)
    @ResponseBody
    public Result<String> handleNotRoleException(NotRoleException e) {
        log.info("GlobalException-->NotRoleException【{}】", e.getMessage());
        e.printStackTrace();
        return Result.error(ResultCode.NotGrant, e.getMessage());
    }

    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseBody
    public Result<String> handleMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        log.info("GlobalException-->handleMethodNotSupportedException【{}】", e.getMessage());
        e.printStackTrace();
        return Result.error(ResultCode.METHOD_NOT_ALLOWED);
    }


    @ExceptionHandler(NotLoginException.class)
    @ResponseBody
    public Result<Object> handleNotLoginException(NotLoginException e) {
        log.info("GlobalException-->NotLoginException【{}】", e.getMessage());
//        e.printStackTrace();
        return Result.error(ResultCode.NoLoginError, e.getMessage(), null);
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler({org.springframework.web.bind.MethodArgumentNotValidException.class, org.springframework.validation.BindException.class})
    @ResponseBody
    public Result<String> handleValidationException(Exception e) {
        log.info("GlobalException-->ValidationException【{}】", e.getMessage());
        org.springframework.validation.BindingResult bindingResult;
        if (e instanceof org.springframework.web.bind.MethodArgumentNotValidException) {
            bindingResult = ((org.springframework.web.bind.MethodArgumentNotValidException) e).getBindingResult();
        } else {
            bindingResult = ((org.springframework.validation.BindException) e).getBindingResult();
        }
        String message = bindingResult.getFieldErrors().stream()
                .map(fieldError -> fieldError.getField() + ":" + fieldError.getDefaultMessage())
                .findFirst()
                .orElse("参数验证失败");
        return Result.error(ResultCode.ValidateError, message);
    }
}
