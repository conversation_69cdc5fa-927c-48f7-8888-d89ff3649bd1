package cn.dahe.dto;

import cn.dahe.entity.ArticleCheck;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 内容校对结果DTO
 */
@Data
@Accessors(chain = true)
public class ContentCheckResultDto {
    /**
     * 是否校对成功
     */
    private boolean success;

    /**
     * 校对结果列表
     */
    private List<ArticleCheck> checkResults;

    /**
     * 错误类型：1-本地异常，2-接口超时，3-接口失败
     */
    private Integer errorType;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 错误详情
     */
    private String errorDetail;

    /**
     * 接口响应时间（毫秒）
     */
    private Long responseTime;

    /**
     * 构建成功结果
     */
    public static ContentCheckResultDto success(List<ArticleCheck> checkResults, Long responseTime) {
        return new ContentCheckResultDto()
                .setSuccess(true)
                .setCheckResults(checkResults)
                .setResponseTime(responseTime);
    }

    /**
     * 构建失败结果
     */
    public static ContentCheckResultDto error(Integer errorType, String errorMessage, String errorDetail) {
        return new ContentCheckResultDto()
                .setSuccess(false)
                .setErrorType(errorType)
                .setErrorMessage(errorMessage)
                .setErrorDetail(errorDetail);
    }

    /**
     * 本地异常
     */
    public static ContentCheckResultDto localError(String message, String detail) {
        return error(1, message, detail);
    }

    /**
     * 接口超时
     */
    public static ContentCheckResultDto timeoutError(String message, String detail) {
        return error(2, message, detail);
    }

    /**
     * 接口失败
     */
    public static ContentCheckResultDto apiError(String message, String detail) {
        return error(3, message, detail);
    }
}