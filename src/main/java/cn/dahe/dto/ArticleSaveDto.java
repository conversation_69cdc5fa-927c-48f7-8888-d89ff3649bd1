package cn.dahe.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文章保存参数DTO
 */
@Data
@Accessors(chain = true)
public class ArticleSaveDto {
    /**
     * 网站ID
     */
    private Long websiteId;

    /**
     * 标题
     */
    private String title;

    /**
     * 链接
     */
    private String url;

    /**
     * 发布时间
     */
    private Date pubTime;

    /**
     * 来源
     */
    private String source;

    /**
     * 作者
     */
    private String author;

    /**
     * 栏目ID
     */
    private Long channelId;
    /**
     * 成文时间
     */
    private Date writeTime;

}