package cn.dahe.dto;

import cn.dahe.entity.WebsiteDeadLinkCheckRecord;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@TableName("t_website_dead_link_check_record")
public class WebsiteDeadLinkCheckRecordDto extends WebsiteDeadLinkCheckRecord {

    /**
     * 总访问次数
     */
    private int checkCount;


}
