package cn.dahe.dto;

import lombok.Data;

/**
 * 文章错误统计DTO
 */
@Data
public class ArticleCheckStatsDto {
    
    /**
     * 网站ID
     */
    private Long websiteId;
    
    /**
     * 网站名称
     */
    private String websiteName;
    
    /**
     * 文章总数
     */
    private Integer articleCount;
    
    /**
     * 文章总字数
     */
    private Integer articleLength;
    
    /**
     * 错误总数
     */
    private Integer checkCount;

    private Integer lv1CheckCount;

    private Integer lv2CheckCount;

    private Integer lv3CheckCount;

    private Integer lv4CheckCount;

    private Integer lv5CheckCount;
}
