package cn.dahe.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 网站错误统计VO
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Schema(name = "网站错误统计", description = "网站错误统计信息")
public class WebsiteErrorStatisticsVO {

    @Schema(description = "网站ID", example = "1")
    private Long websiteId;

    @Schema(description = "网站名称", example = "河南省人民政府")
    private String websiteName;

    @Schema(description = "网站地址", example = "https://www.henan.gov.cn")
    private String websiteUrl;

    @Schema(description = "分组名称", example = "政府网站")
    private String groupName;

    @Schema(description = "文章数量", example = "1250")
    private Long articleCount;

    @Schema(description = "严重错误数量", example = "5")
    private Long severeErrorCount;

    @Schema(description = "一般错误数量", example = "12")
    private Long normalErrorCount;

    @Schema(description = "轻微错误数量", example = "8")
    private Long minorErrorCount;

    @Schema(description = "总错误数量", example = "25")
    private Long totalErrorCount;

    @Schema(description = "错误率", example = "2.0%")
    private String errorRate;

    @Schema(description = "最后检查时间", example = "2025-07-30 10:30:00")
    private String lastCheckTime;

    @Schema(description = "检查状态", example = "正常")
    private String checkStatus;

    @Schema(description = "操作", example = "查看详情")
    private String operation;
}
