package cn.dahe.vo;

import cn.dahe.enums.CheckStrategyEnum;
import cn.dahe.service.WebsiteGroupService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 网站信息VO
 */
@Data
@Schema(name = "网站信息", description = "网站基本信息")
public class WebsiteVO {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "网站名称")
    private String webName;

    @Schema(description = "网站地址")
    private String webUrl;

    @Schema(description = "网站标识码")
    private String webCodeId;

    @Schema(description = "分组ID")
    private Integer groupId;

    @Schema(description = "分组名称")
    private String groupName;

    @Schema(description = "检查策略")
    private Integer checkStrategy;

    @Schema(description = "检查策略描述")
    public String getCheckStrategyDesc() {
        return CheckStrategyEnum.getDesc(this.checkStrategy);
    }

    public String getGroupName() {
        return WebsiteGroupService.getGroupName(this.groupId);
    }
} 