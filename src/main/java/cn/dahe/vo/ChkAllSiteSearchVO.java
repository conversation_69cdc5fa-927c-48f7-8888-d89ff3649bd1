package cn.dahe.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 全站搜索VO
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Data
@Schema(name = "全站搜索视图对象", description = "全站搜索记录信息视图")
public class ChkAllSiteSearchVO {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "网站名称", example = "河南师范大学")
    private String websiteName;

    @Schema(description = "分组名称", example = "高校网站")
    private String groupName;

    @Schema(description = "文章标题", example = "关于开展教学质量评估的通知")
    private String title;

    @Schema(description = "文章内容摘要", example = "为进一步提高教学质量...")
    private String contentSummary;

    @Schema(description = "文章URL", example = "https://www.htu.edu.cn/news/123.html")
    private String url;

    @Schema(description = "发布时间", example = "2025-08-04 10:30:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    @Schema(description = "内容类型名称", example = "文章")
    private String contentTypeName;

    @Schema(description = "过滤状态名称", example = "未过滤")
    private String filterStatusName;

    @Schema(description = "信源网站", example = "河南省人民政府")
    private String sourceWebsite;

    @Schema(description = "附件名称", example = "通知文件.pdf")
    private String attachmentName;

    @Schema(description = "附件URL", example = "https://www.htu.edu.cn/files/notice.pdf")
    private String attachmentUrl;

    @Schema(description = "附件大小", example = "1.5MB")
    private String attachmentSize;

    @Schema(description = "关键词匹配度", example = "85%")
    private String matchRate;

    @Schema(description = "创建时间", example = "2025-08-04 10:30:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    // ==================== 扩展字段 ====================

    @Schema(description = "内容类型：1文章，2附件", example = "1")
    private Integer contentType;

    @Schema(description = "是否过滤转载：0未过滤，1已过滤", example = "0")
    private Integer isFiltered;

    @Schema(description = "完整内容", example = "完整的文章内容...")
    private String content;
}
