package cn.dahe.vo;

import cn.dahe.service.CheckErrorLevelService;
import cn.dahe.service.CheckErrorTypeService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 网站文章错误词统计VO
 */
@Data
@Schema(description = "网站文章错误词统计")
public class WebsiteArticleCheckWordStatsVO {

    /**
     * 错误词ID
     */
    @Schema(description = "错误词ID")
    private Integer wordId;

    @Schema(description = "错误词", example = "中国人民共国")
    private String errorWord;

    @Schema(description = "建议正确用词", example = "中华人民共和国")
    private String suggestWord;

    /**
     * 错误等级
     */
    @Schema(description = "错误等级")
    private Integer errorLevel;

    /**
     * 错误等级名称
     */
    @Schema(description = "错误等级名称")
    private String errorLevelName;

    public String getErrorLevelName() {
        return CheckErrorLevelService.getLevelName(errorLevel);
    }

    /**
     * 错误类型ID
     */
    @Schema(description = "错误类型ID")
    private Long errorType;

    /**
     * 错误类型名称
     */
    @Schema(description = "错误类型名称")
    private String errorTypeName;

    private String getErrorTypeName(){
        return CheckErrorTypeService.getTypeName(errorType);
    }

    /**
     * 错误出现次数
     */
    @Schema(description = "错误出现次数")
    private Integer checkCount;

    /**
     * 出现该错误的网站数量
     */
    @Schema(description = "出现该错误的网站数量")
    private Integer websiteCount;

    /**
     * 是否已过滤
     */
    @Schema(description = "是否过滤")
    private Boolean isFiltered;

    @Schema(description = "网站列表")
    private List<WebsiteInfo> websites;

    @Data
    @Schema(description = "网站信息")
    public static class WebsiteInfo {
        @Schema(description = "网站ID")
        private Long websiteId;

        @Schema(description = "网站名称")
        private String websiteName;
    }
} 