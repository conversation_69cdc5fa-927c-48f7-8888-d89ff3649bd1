package cn.dahe.vo.check;

import cn.dahe.service.CheckErrorLevelService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 二级错误类型VO类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "二级错误类型信息")
public class SecondLevelErrorTypeVO extends BaseErrorTypeVO {

    @Schema(description = "错误等级")
    private Integer errorLevel;

    @Schema(description = "错误等级名称")
    private String errorLevelName;

    public String getErrorLevelName() {
        return CheckErrorLevelService.getLevelName(errorLevel);
    }

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "三级错误类型列表")
    private List<ThirdLevelErrorTypeVO> children;
} 