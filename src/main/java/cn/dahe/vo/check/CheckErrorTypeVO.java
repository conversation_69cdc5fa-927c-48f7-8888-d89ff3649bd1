package cn.dahe.vo.check;

import cn.dahe.entity.CheckErrorType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 错误类型VO类
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@Schema(name = "错误类型树形结构视图对象")
public class CheckErrorTypeVO extends CheckErrorType {

    /**
     * 子节点列表
     */
    @Schema(description = "子节点列表")
    private List<CheckErrorTypeVO> children;

    /**
     * 错误等级名称
     */
    @Schema(description = "错误等级名称", example = "严重错误")
    private String errorLevelName;
} 