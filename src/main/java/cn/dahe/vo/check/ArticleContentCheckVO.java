package cn.dahe.vo.check;

import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 文章检查结果VO
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
public class ArticleContentCheckVO {
    /**
     * 文章ID
     */
    @Schema(description = "文章ID")
    private Long articleId;
    /**
     * 文章标题
     */
    @Schema(description = "标记后的纯文本标题")
    private String markedCleanTitle;
    /**
     * 文章正文（已标记）
     */
    @Schema(description = "标记后的纯文本内容")
    private String markedCleanContent;

    @Schema(description = "标记后的html标题")
    private String markedHtmlTitle;

    @Schema(description = "标记后的html内容")
    private String markedHtmlContent;

    @Schema(description = "html标题")
    private String htmlTitle;

    @Schema(description = "html内容")
    private String htmlContent;

    @Schema(description = "标题")
    private String cleanedTitle;

    @Schema(description = "内容")
    private String cleanedContent;

    @Schema(description = "文章审核状态：0未审核 1审核通过 2审核驳回", example = "0")
    private Integer auditStatus;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private Date pubTime;

    /**
     * 采集时间
     */
    @Schema(description = "采集时间")
    private Date acquisitionTime;

    /**
     * 检查时间
     */
    @Schema(description = "检测时间")
    private Date checkTime;

    /**
     * 错误数量
     */
    @Schema(description = "错误数量")
    private Integer errorCount;

    public Integer getErrorCount() {
        return CollUtil.size(errorObjs);
    }

    /**
     * 错误列表
     */
    @Schema(description = "错误列表")
    private List<ArticleCheckVO> errorObjs;




} 