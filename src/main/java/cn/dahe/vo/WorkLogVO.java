package cn.dahe.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;


import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-02-26
 */

@Data
public class WorkLogVO {

    private Integer id;
    private Integer workId;
    private Integer userId;

    /**
     * 操作人姓名
     */
    private String userName;

    /**
     * 操作时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateDate;

    /**
     * 操作类型
     */
    private Integer operateType;

    /**
     * 备注信息
     */
    private String remark;
}
