package cn.dahe.vo;

import lombok.Data;

import java.util.HashSet;
import java.util.Set;

@Data
public class LoginUserVO {
    private static final long serialVersionUID = 1L;

    /**
     * 用户名id
     */
    private Integer userId;


    /**
     * 用户名
     */
    private String username;

    /**
     * 真实名称
     */
    private String truename;


    /**
     * 账户
     */
    private String account;

    /**
     * 部门ID
     */
    private Integer depId;

    /**
     * 部门名称
     */
    private String depName;


    /**
     * 手机号
     */
    private String phone;


    /**
     * 区划
     */
    private Integer cityId;


    /**
     * 权限列表
     */
    private Set<String> permissions = new HashSet<>();

    /**
     * 按钮列表
     */
    private Set<String> buttonPermissions = new HashSet<>();
    /**
     * 列表权限
     */
    private Set<String> listPermissions = new HashSet<>();

    /**
     * 角色列表
     */
    private Set<String> roles = new HashSet<>();
}
