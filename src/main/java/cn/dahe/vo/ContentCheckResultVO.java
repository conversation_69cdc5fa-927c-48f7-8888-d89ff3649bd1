package cn.dahe.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 内容检查结果VO
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Schema(name = "内容检查结果", description = "内容检查结果信息")
public class ContentCheckResultVO {

    @Schema(description = "内容ID", example = "1")
    private Long contentId;

    @Schema(description = "内容标题", example = "关于加强网站建设的通知")
    private String contentTitle;

    @Schema(description = "来源网站", example = "河南省人民政府")
    private String sourceWebsite;

    @Schema(description = "发布时间", example = "2025-07-30 09:00:00")
    private String publishTime;

    @Schema(description = "检测时间", example = "2025-07-30 10:30:00")
    private String checkTime;

    @Schema(description = "检测结果", example = "发现问题")
    private String checkResult;

    @Schema(description = "问题类型", example = "错别字")
    private String issueType;

    @Schema(description = "问题描述", example = "标题中存在错别字")
    private String issueDescription;

    @Schema(description = "严重程度", example = "一般")
    private String severity;

    @Schema(description = "处理状态", example = "待处理")
    private String processStatus;

    @Schema(description = "处理人", example = "张三")
    private String processor;

    @Schema(description = "处理时间", example = "2025-07-30 11:00:00")
    private String processTime;

    @Schema(description = "操作", example = "查看详情")
    private String operation;
}
