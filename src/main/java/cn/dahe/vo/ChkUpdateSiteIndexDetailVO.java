package cn.dahe.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 首页更新检查详情VO - 具体文章记录
 * 用于展示某个网站的具体文章列表
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Data
@Schema(name = "首页更新检查详情视图对象", description = "网站首页更新检查详情信息")
public class ChkUpdateSiteIndexDetailVO {

    @Schema(description = "记录ID", example = "1")
    private Long id;

    @Schema(description = "网站ID", example = "1")
    private Long websiteId;

    @Schema(description = "分组ID", example = "1")
    private Long groupId;

    @Schema(description = "分组名称", example = "政府网站")
    private String groupName;

    @Schema(description = "网站名称", example = "河南省人民政府")
    private String websiteName;

    @Schema(description = "网站首页地址", example = "https://www.henan.gov.cn")
    private String websiteIndexUrl;

    @Schema(description = "文章标题", example = "关于印发河南省数字政府建设总体规划的通知")
    private String articleTitle;

    @Schema(description = "文章内容摘要", example = "为加快推进数字政府建设...")
    private String articleContentSummary;

    @Schema(description = "文章地址", example = "https://www.henan.gov.cn/2025/07-30/123456.html")
    private String articleUrl;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "文章发布时间", example = "2025-07-30 10:30:00")
    private Date articlePublishTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "解析时间", example = "2025-07-30 10:30:00")
    private Date parseTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    private Date createTime;

    @Schema(description = "操作", example = "查看内容")
    private String operation;

    // ==================== 扩展字段 ====================

    @Schema(description = "文章完整内容", example = "完整的文章内容...")
    private String articleContent;

    @Schema(description = "是否为今日文章", example = "true")
    private Boolean isTodayArticle;

    @Schema(description = "发布天数", example = "3")
    private Integer publishDays;
}
