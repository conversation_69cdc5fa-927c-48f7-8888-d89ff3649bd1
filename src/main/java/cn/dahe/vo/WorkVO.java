package cn.dahe.vo;

import cn.dahe.entity.Attachment;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.DefaultValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Data
public class WorkVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;
    private Integer userId;
    private String title;
    private String depName;
    private String authorName;
    private String editName;
    private String originDepName;
    private String publishPlatform;
    private String publishPage;
    private String publishRate;
    private String publishCycle;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date publishDate;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date publishEndDate;
    private String newMediaUrl;
    private String reportTitle;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitAuditTime;
    private String attachIdNewMediaQr;
    private String attachIdDepPublic;
    private String attachIdDepPromise;
    private String attachIdDepCatalog;
    private String attachIdDepCatalogPdf;
    private String attachIdSocialEffect;

    private String dictIdProject;
    private String dictIdCategory;
    private String dictIdLanguage;
    private String dictIdGenre;
    private String dictIdMedia;


    private Integer status;
    private String yearPublic;
    private Integer newsRewardId;

    @Column(name = "work_num")
    @Schema(description = "作品编号")
    private String workNum;

    @Column(name = "work_num_final")
    @Schema(description = "定评作品编号")
    private String workNumFinal;


    //*********** 材料2 ***********
    @ColumnComment("附件ID-推荐表扫描文件PDF")
    private String attachIdRecommendPdf;
    @ColumnComment("附件ID-推荐表扫描文件WORD版")
    private String attachIdRecommendWord;
    //*********** 材料3 ***********
    private String attahIdMaterialThree1;
    private String attahIdMaterialThree2;
    private String attahIdMaterialThree3;
    private String attahIdMaterialThree4;
    private String attahIdMaterialThree5;
    private String attahIdMaterialThree6;
    private String attahIdMaterialThree7;

    //*******材料作品4**********
    @ColumnComment("附件ID-承诺书")
    private String attachIdPromise;
    @ColumnComment("附件ID-其他补充材料")
    private String attachIdOther;


    //***********作品大字段************
    private String contentIntroduction;
    private String contentSocialEffect;
    private String contentRecommended;

    //**********************************************附件List**********************************************
    private List<Attachment> attachIdSocialEffectList = new ArrayList<>();
    private List<Attachment> attachIdNewMediaQrList = new ArrayList<>();
    //*********** 材料2 ***********
    private List<Attachment> attachIdRecommendPdfList = new ArrayList<>();
    private List<Attachment> attachIdRecommendWordList = new ArrayList<>();
    //*********** 材料3 ***********
    private List<Attachment> attahIdMaterialThree1List = new ArrayList<>();
    private List<Attachment> attahIdMaterialThree2List = new ArrayList<>();
    private List<Attachment> attahIdMaterialThree3List = new ArrayList<>();
    private List<Attachment> attahIdMaterialThree4List = new ArrayList<>();
    private List<Attachment> attahIdMaterialThree5List = new ArrayList<>();
    private List<Attachment> attahIdMaterialThree6List = new ArrayList<>();
    private List<Attachment> attahIdMaterialThree7List = new ArrayList<>();
    //*******材料作品4**********
    private List<Attachment> attachIdPromiseList = new ArrayList<>();
    private List<Attachment> attachIdOtherList = new ArrayList<>();
    //*******单位相关************
    private List<Attachment> attachIdDepPublicList = new ArrayList<>();
    private List<Attachment> attachIdDepCatalogList = new ArrayList<>();
    private List<Attachment> attachIdDepPromiseList = new ArrayList<>();
    private List<Attachment> attachIdDepCatalogPdfList = new ArrayList<>();


    //***************报送人推荐人List***********************
    private List<WorkReportUserVO> workReportUserVOList = new ArrayList<>();


    //************************评审相关************************
    private Integer reviewStatus;
    private Integer awardLevelId;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewDate;
    private String reviewRemark;
    private String reviewExpertStr = "";
    private Integer workEvaluatStatus;


    //************************管理员评审************************
    @Column(name = "admin_first_award_level_id")
    @DefaultValue("0")
    private Integer adminFirstAwardLevelId;

    @Column(name = "admin_final_award_level_id")
    @DefaultValue("0")
    private Integer adminFinalAwardLevelId;
}