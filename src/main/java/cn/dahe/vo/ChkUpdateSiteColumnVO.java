package cn.dahe.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 栏目更新检查VO - 对应原型图字段
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Schema(name = "栏目更新检查视图对象", description = "网站栏目更新检查信息视图")
public class ChkUpdateSiteColumnVO {

    @Schema(description = "编号", example = "1")
    private Long id;

    @Schema(description = "序号", example = "1")
    private Integer serialNumber;

    @Schema(description = "栏目名称", example = "新闻动态")
    private String columnName;

    @Schema(description = "栏目分类", example = "新闻类")
    private String columnCategory;

    @Schema(description = "更新时间", example = "2025-07-30 10:30:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "检测状态", example = "正常")
    private String checkStatus;

    @Schema(description = "连续不更新天数", example = "3")
    private Integer continuousNotUpdateDays;

    @Schema(description = "检测结果", example = "通过")
    private String checkResult;

    @Schema(description = "操作", example = "内容明细")
    private String operation;

    // ==================== 扩展字段 ====================

    @Schema(description = "分组名称", example = "政府网站")
    private String groupName;

    @Schema(description = "栏目ID", example = "1")
    private Long columnId;

    @Schema(description = "快照URL", example = "https://snapshot.example.com/123456")
    private String snapshotUrl;

    @Schema(description = "快照签名", example = "abc123def456")
    private String snapshotSignature;

    @Schema(description = "解析时间", example = "2025-07-30 10:30:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date parseTime;

    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
