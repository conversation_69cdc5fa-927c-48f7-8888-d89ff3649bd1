package cn.dahe.vo;

import cn.dahe.service.WebsiteGroupService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 网站文章错误统计VO
 */
@Data
@Schema(description = "网站文章错误统计")
public class WebsiteArticleCheckStatsVO {

    /**
     * 网站ID
     */
    @Schema(description = "网站ID")
    private Long websiteId;

    @Schema(description = "网站名称")
    private String websiteName;

    @Schema(description = "网站URL")
    private String websiteUrl;

    /**
     * 分组ID
     */
    @Schema(description = "分组ID")
    private Integer groupId;

    /**
     * 分组名称
     */
    @Schema(description = "分组名称")
    private String groupName;

    public String getGroupName() {
        return WebsiteGroupService.getGroupName(this.groupId);
    }


    /**
     * 文章总数
     */
    @Schema(description = "文章总数")
    private Integer articleCount;

    /**
     * 文章总字数
     */
    @Schema(description = "文章总字数")
    private Integer articleLength;

    /**
     * 错误总数
     */
    @Schema(description = "错误总数")
    private Integer checkCount;

    /**
     * 一级错误数量
     */
    @Schema(description = "一级错误数量")
    private Integer lv1CheckCount;

    /**
     * 二级错误数量
     */
    @Schema(description = "二级错误数量")
    private Integer lv2CheckCount;

    /**
     * 三级错误数量
     */
    @Schema(description = "三级错误数量")
    private Integer lv3CheckCount;

    /**
     * 四级错误数量
     */
    @Schema(description = "四级错误数量")
    private Integer lv4CheckCount;

    /**
     * 五级错误数量
     */
    @Schema(description = "五级错误数量")
    private Integer lv5CheckCount;
} 