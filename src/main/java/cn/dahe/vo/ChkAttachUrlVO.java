package cn.dahe.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 附件检查VO - 对应原型图字段
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Schema(name = "附件检查视图对象", description = "网站附件检查信息视图")
public class ChkAttachUrlVO {

    @Schema(description = "编号", example = "1")
    private Long id;

    @Schema(description = "网站名称", example = "河南师范大学")
    private String websiteName;

    @Schema(description = "来源网站", example = "河南省人民政府")
    private String sourceWebsite;

    @Schema(description = "来源页面", example = "https://www.henan.gov.cn/policy/1")
    private String sourcePage;

    @Schema(description = "发布时间", example = "2025-07-30 10:30:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    @Schema(description = "检测时间", example = "2025-07-30 10:30:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    @Schema(description = "操作", example = "内容")
    private String operation;

    // ==================== 扩展字段 ====================

    @Schema(description = "附件名称", example = "政策文件.pdf")
    private String attachName;

    @Schema(description = "附件地址", example = "https://www.example.gov.cn/files/attach.pdf")
    private String attachUrl;

    @Schema(description = "附件类型", example = "PDF")
    private String attachType;

    @Schema(description = "附件大小", example = "1.5MB")
    private String attachSize;

    @Schema(description = "检测状态", example = "正常")
    private String checkStatus;

    @Schema(description = "创建时间", example = "2025-07-30 10:30:00")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
