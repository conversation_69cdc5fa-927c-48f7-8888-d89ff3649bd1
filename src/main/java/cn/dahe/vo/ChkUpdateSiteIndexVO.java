package cn.dahe.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.ColumnComment;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsAutoIncrement;
import com.gitee.sunchenbin.mybatis.actable.annotation.IsKey;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 首页更新检查VO - 对应原型图字段
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@Schema(name = "首页更新检查视图对象", description = "网站首页更新检查信息视图")
public class ChkUpdateSiteIndexVO {

    @Schema(description = "编号", example = "1")
    private Long id;

    @Schema(description = "分组", example = "政府网站")
    private String groupName;

    @Schema(description = "网站名称", example = "河南省人民政府")
    private String websiteName;

    @Schema(description = "网站首页地址", example = "https://www.henan.gov.cn")
    private String websiteIndexUrl;

    @Schema(description = "首页是否更新：0未更新，1已更新", example = "1")
    private Integer status;

    @Schema(description = "更新天数", example = "1")
    private Integer updateDates;

    @Schema(description = "连续未更新天数", example = "1")
    private Integer continuousNotUpdateDates;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "解析时间", example = "2025-07-30 10:30:00")
    private Date lastParseTime;
}
